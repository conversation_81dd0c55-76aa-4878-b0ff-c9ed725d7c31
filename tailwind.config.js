const colors = require('tailwindcss/colors');
const defaultTheme = require('tailwindcss/defaultTheme');

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        "./resources/**/*.blade.php",
        "./resources/**/*.js",
        "./resources/**/*.vue",
    ],
    theme: {
        extend: {
            colors: {
                'primary': {
                    ...colors.sky,
                    DEFAULT: colors.sky[500],
                },
                gray: {
                    ...colors.neutral
                }
            },
            fontFamily: {
                sans: ['Mona sans', 'sans-serif']
            }
        },
    },
    plugins: [
        require('@tailwindcss/typography'),
    ],
}

