<?php

use App\Models\User;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Mail;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

Artisan::command('dau', function () {
    $bunny = app(\App\Services\Bunny::class);

    $result = $bunny->request('get', '/statistics', ['pullZone' => 2888189]);

    dd($result);
});
