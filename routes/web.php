<?php

use App\Http\Controllers\BlogController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\PrivacyController;
use App\Http\Controllers\TermsController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\PricingController;
use App\Http\Controllers\UseCasesController;
use App\Mail\WelcomeEmail;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Mail;

Route::get('/', HomeController::class)->name('home');

Route::get('/terms', TermsController::class)->name('terms');
Route::get('/privacy', PrivacyController::class)->name('privacy');
Route::get('/contact', ContactController::class)->name('contact');
Route::get('/pricing', PricingController::class)->name('pricing');
Route::view('/about', 'about')->name('about');

Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog.show');

Route::get('/buy/{planId}', [CheckoutController::class, 'create'])->name('checkout.create');

Route::get('/use-cases', [UseCasesController::class, 'index'])->name('use-cases');
Route::get('/use-cases/ecommerce', [UseCasesController::class, 'ecommerce'])->name('use-cases.ecommerce');
Route::get('/use-cases/content-management', [UseCasesController::class, 'contentManagement'])->name('use-cases.content-management');
Route::get('/use-cases/subscription', [UseCasesController::class, 'subscription'])->name('use-cases.subscription');
Route::get('/use-cases/api', [UseCasesController::class, 'api'])->name('use-cases.api');
Route::get('/use-cases/analytics', [UseCasesController::class, 'analytics'])->name('use-cases.analytics');

Route::view('affiliate', 'pages.affiliate')->name('affiliate');

Route::get('/login', function () {
    return to_route('filament.dashboard.auth.login', [], 301);
});

Route::get('/register', function () {
    return to_route('filament.dashboard.auth.register', [], 301);
});

/**
 * Social Login
 */
Route::get('/login/{social}', [LoginController::class, 'login'])->name('login.social');
Route::get('/login/{social}/callback', [LoginController::class, 'callback'])->name('login.social.callback');

Route::get('/logout', function () {
    Auth::logout();

    return redirect('/');
});

Route::view('/stress-test', 'stress-test');

Route::get('/test-mail', function () {
    $user = User::find(1);
    Mail::to('<EMAIL>')->sendNow(new WelcomeEmail($user));

    return 'Mail sent successfully!';
});
