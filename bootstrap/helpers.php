<?php

use App\Support\SkymageCDN;

if (!function_exists('skymagecdn')) {
    /**
     * Transform an image URL to use the Skymage CDN.
     */
    function skymagecdn(string $imageUrl, array $options = []): string
    {
        return app(SkymageCDN::class)->transfer($imageUrl, $options);
    }
}

if (!function_exists('generate_jsonld')) {
    /**
     * Generate JSON-LD structured data for the website.
     */
    function generate_jsonld(?string $title = null, ?string $description = null, ?string $imageUrl = null): string
    {
        $title = $title ?? config('app.name');
        $description = $description ?? "Optimize images on the fly with Skymage's fast CDN. Resize, crop, and compress with our simple API for developers";
        $imageUrl = $imageUrl ?? 'https://skymage.daudau.cc/og.png';

        $jsonLd = [
            '@context' => 'https://schema.org',
            '@type' => 'SoftwareApplication',
            'name' => $title,
            'description' => $description,
            'applicationCategory' => 'DeveloperApplication',
            'operatingSystem' => 'Web',
            'image' => $imageUrl,
            'url' => config('app.url'),
            'author' => [
                '@type' => 'Organization',
                'name' => 'Skymage CDN',
                'url' => 'https://skymage.daudau.cc',
                'logo' => 'https://skymage.daudau.cc/logo.svg',
                'sameAs' => [
                    'https://twitter.com/bangnokia',
                    'https://github.com/skymagephp',
                    'https://www.linkedin.com/company/skymage'
                ]
            ],
            'offers' => [
                '@type' => 'Offer',
                'price' => '9',
                'priceCurrency' => 'USD',
                'availability' => 'https://schema.org/InStock',
                'url' => 'https://skymage.daudau.cc/pricing',
                'description' => '14-day free trial. No credit card required.'
            ],
            'aggregateRating' => [
                '@type' => 'AggregateRating',
                'ratingValue' => '4.8',
                'ratingCount' => '120'
            ],
            'hasPart' => [
                // Blog section
                [
                    '@type' => 'WebPage',
                    'url' => 'https://skymage.daudau.cc/blog',
                    'name' => 'Blog',
                    'description' => 'Latest articles about image optimization and Skymage CDN'
                ],
                // Docs section
                [
                    '@type' => 'WebPage',
                    'url' => 'https://skymage.daudau.cc/docs',
                    'name' => 'Documentation',
                    'description' => 'Developer documentation for Skymage CDN'
                ],
                // Pricing section
                [
                    '@type' => 'WebPage',
                    'url' => 'https://skymage.daudau.cc/pricing',
                    'name' => 'Pricing',
                    'description' => 'Skymage CDN pricing plans'
                ],
                // Affiliate section
                [
                    '@type' => 'WebPage',
                    'url' => 'https://skymage.daudau.cc/affiliate',
                    'name' => 'Affiliate Program',
                    'description' => 'Join our affiliate program and earn rewards'
                ]
            ]
        ];

        return '<script type="application/ld+json">' . json_encode($jsonLd, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>';
    }
}
