<?php

namespace Database\Factories;

use App\Enums\ExternalStorageDriver;
use App\Enums\ExternalStorageStatus;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ExternalStorage>
 */
class ExternalStorageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $driver = fake()->randomElement(ExternalStorageDriver::cases());

        return [
            'user_id' => User::factory(),
            'name' => fake()->words(2, true) . ' Storage',
            'handle' => fake()->unique()->slug(2) . '-' . fake()->randomNumber(3),
            'driver' => $driver,
            'configuration' => $this->generateConfiguration($driver),
            'status' => fake()->randomElement(ExternalStorageStatus::cases()),
        ];
    }

    /**
     * Indicate that the storage should be connected.
     */
    public function connected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => ExternalStorageStatus::Connected,
        ]);
    }

    /**
     * Generate configuration based on driver.
     */
    private function generateConfiguration(ExternalStorageDriver $driver): array
    {
        return match ($driver) {
            ExternalStorageDriver::Bunny => [
                'api_key' => fake()->regexify('[a-f0-9]{64}'),
                'storage_zone' => fake()->word(),
                'region' => fake()->randomElement(['de', 'ny', 'la', 'sg']),
            ],
            ExternalStorageDriver::S3 => [
                'access_key_id' => fake()->regexify('[A-Z0-9]{20}'),
                'secret_access_key' => fake()->regexify('[a-zA-Z0-9+/]{40}'),
                'bucket' => fake()->slug(),
                'region' => fake()->randomElement(['us-east-1', 'us-west-2', 'eu-west-1']),
                'endpoint' => fake()->optional(0.3)->url(),
            ],
            ExternalStorageDriver::FTP => [
                'host' => fake()->domainName(),
                'port' => fake()->randomElement([21, 22, 990]),
                'username' => fake()->userName(),
                'password' => fake()->password(),
                'root_path' => '/',
                'ssl' => fake()->boolean(30),
                'passive' => fake()->boolean(80),
            ],
        };
    }
}
