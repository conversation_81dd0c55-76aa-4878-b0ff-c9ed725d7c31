<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('pull_zones', function (Blueprint $table) {
             $table->string('status', 32)->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('pull_zones', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
