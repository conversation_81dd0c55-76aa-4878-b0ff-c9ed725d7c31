<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('external_storages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->string('handle', 64);
            $table->string('driver'); // bunny, s3, ftp
            $table->json('configuration'); // encrypted configuration
            $table->string('status')->default('disconnected'); // connected, disconnected
            $table->timestamps();

            $table->unique(['user_id', 'handle']); // unique handle per user
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('external_storages');
    }
};
