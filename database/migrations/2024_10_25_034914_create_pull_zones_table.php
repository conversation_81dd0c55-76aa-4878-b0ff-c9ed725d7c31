<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pull_zones', function (Blueprint $table) {
            $table->id();
            $table->string('handle', 64)->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('id_bunny')->nullable()->unique();
            $table->json('data_bunny')->nullable()->comment('the data of Bunny pull zone');
            $table->string('custom_hostname')->nullable()->index();
            $table->string('cf_dns_record_id')->nullable()->unique();
            $table->json('settings')->nullable()->comment('transformation settings');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pull_zones');
    }
};
