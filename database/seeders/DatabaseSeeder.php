<?php

namespace Database\Seeders;

use App\Models\User;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $dau = User::create([
            'name'     => 'Nguyen Viet',
            'email'    => '<EMAIL>',
            'password' => '$2y$12$yqFP4TcqMkjGJjcFgnOXfeOxCVEebZfrHUYAPxY1aqfi0T/uN0JuC',
        ]);

        $dau->pullZone()->create([
            'handle'     => 'dau',
            'settings'   => [
                'enable_domains_allow_list' => false,
                'domains'                  => [
                    'daudau.cc',
                    'github.com',
                    'skymage.net',
                ],
                'add_canonical_header'     => true,
                'jpeg_compression'         => 85,
            ]
        ]);
    }
}
