<?php

namespace Database\Seeders;

use App\Models\ExternalStorage;
use App\Models\User;
use Illuminate\Database\Seeder;

class ExternalStorageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test user if none exists
        $user = User::first() ?? User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Create sample external storages
        ExternalStorage::create([
            'user_id' => $user->id,
            'name' => 'My S3 Storage',
            'handle' => 'my-s3-storage',
            'driver' => 's3',
            'configuration' => [
                'access_key_id' => 'AKIAIOSFODNN7EXAMPLE',
                'secret_access_key' => 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                'bucket' => 'my-s3-bucket',
                'region' => 'us-east-1',
            ],
            'status' => 'connected',
        ]);

        ExternalStorage::create([
            'user_id' => $user->id,
            'name' => 'Bunny Storage',
            'handle' => 'bunny-storage',
            'driver' => 'bunny',
            'configuration' => [
                'api_key' => 'bunny-api-key-example',
                'storage_zone' => 'my-storage-zone',
                'region' => 'de',
            ],
            'status' => 'connected',
        ]);

        ExternalStorage::create([
            'user_id' => $user->id,
            'name' => 'FTP Server',
            'handle' => 'ftp-server',
            'driver' => 'ftp',
            'configuration' => [
                'host' => 'ftp.example.com',
                'port' => 21,
                'username' => 'ftpuser',
                'password' => 'ftppassword',
                'root_path' => '/',
                'ssl' => false,
                'passive' => true,
            ],
            'status' => 'disconnected',
        ]);

        $this->command->info('Created sample external storage configurations');
    }
}
