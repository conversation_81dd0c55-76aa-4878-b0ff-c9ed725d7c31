<svg xmlns="http://www.w3.org/2000/svg" width="600" height="600" viewBox="0 0 600 600"><foreignObject width="100%" height="100%" x="0" y="0" externalResourcesRequired="true"><div xmlns="http://www.w3.org/1999/xhtml" class="bg-transparent w-screen max-w-full aspect-square md:w-[350px] md:h-[350px] lg:w-[400px] lg:h-[400px] xl:w-[600px] xl:h-[600px]" style="accent-color: auto; place-content: normal; place-items: normal; place-self: auto; animation-composition: replace; animation: none; appearance: none; aspect-ratio: 1 / 1; backdrop-filter: none; backface-visibility: visible; background: transparent; background-blend-mode: normal; baseline-source: auto; block-size: 600px; border-block: 0px solid rgb(229, 231, 235); border-bottom: 0px solid rgb(229, 231, 235); border-bottom-left-radius: 0px; border-bottom-right-radius: 0px; border-collapse: separate; border-end-end-radius: 0px; border-end-start-radius: 0px; border-image: none; border-inline: 0px solid rgb(229, 231, 235); border-left: 0px solid rgb(229, 231, 235); border-right: 0px solid rgb(229, 231, 235); border-spacing: 0px; border-start-end-radius: 0px; border-start-start-radius: 0px; border-top: 0px solid rgb(229, 231, 235); border-top-left-radius: 0px; border-top-right-radius: 0px; bottom: auto; box-decoration-break: slice; box-shadow: none; box-sizing: border-box; break-after: auto; break-before: auto; break-inside: auto; caption-side: top; caret-color: rgba(48, 48, 48, 0.8); clear: none; clip: auto; clip-path: none; clip-rule: nonzero; color: rgba(48, 48, 48, 0.8); color-interpolation: srgb; color-interpolation-filters: linearrgb; color-scheme: light; columns: auto; column-fill: balance; gap: normal; column-rule: 0px none rgba(48, 48, 48, 0.8); column-span: none; contain: none; contain-intrinsic-block-size: none; contain-intrinsic-height: none; contain-intrinsic-inline-size: none; contain-intrinsic-width: none; container: none; content: normal; content-visibility: visible; counter-increment: none; counter-reset: none; counter-set: none; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: block; dominant-baseline: auto; empty-cells: show; fill: rgb(0, 0, 0); fill-opacity: 1; fill-rule: nonzero; filter: none; flex: 0 1 auto; flex-flow: row; float: none; flood-color: rgb(0, 0, 0); flood-opacity: 1; font: 15.9px / 24px __Space_Grotesk_587f35, __Space_Grotesk_Fallback_587f35; font-palette: normal; font-synthesis: weight style small-caps position; forced-color-adjust: auto; grid: none; grid-area: auto; height: 600px; hyphenate-character: auto; hyphens: manual; image-orientation: from-image; image-rendering: auto; ime-mode: auto; inline-size: 600px; inset-block: auto; inset-inline: auto; isolation: auto; left: auto; letter-spacing: normal; lighting-color: rgb(255, 255, 255); line-break: auto; list-style: outside; margin-block: 0px; margin-bottom: 0px; margin-inline: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; marker: none; mask: none; mask-type: luminance; math-depth: 0; math-style: normal; max-block-size: none; max-height: none; max-inline-size: 100%; max-width: 100%; min-block-size: 0px; min-height: 0px; min-inline-size: 0px; min-width: 0px; mix-blend-mode: normal; object-fit: fill; object-position: 50% 50%; offset: normal; opacity: 1; order: 0; outline: rgba(48, 48, 48, 0.8) 0px; outline-offset: 0px; overflow-anchor: auto; overflow-block: visible; overflow-clip-margin: 0px; overflow-inline: visible; overflow-wrap: normal; overflow: visible; overscroll-behavior-block: auto; overscroll-behavior-inline: auto; overscroll-behavior: auto; padding-block: 45px; padding-bottom: 45px; padding-inline: 45px; padding-left: 45px; padding-right: 45px; padding-top: 45px; page: auto; paint-order: normal; perspective: none; perspective-origin: 300px 300px; pointer-events: auto; position: static; print-color-adjust: economy; quotes: auto; r: 0px; resize: none; right: auto; rotate: none; ruby-align: space-around; ruby-position: alternate; rx: auto; ry: auto; scale: none; scroll-behavior: auto; scroll-margin-block: 0px; scroll-margin-bottom: 0px; scroll-margin-inline: 0px; scroll-margin-left: 0px; scroll-margin-right: 0px; scroll-margin-top: 0px; scroll-padding-block: auto; scroll-padding-bottom: auto; scroll-padding-inline: auto; scroll-padding-left: auto; scroll-padding-right: auto; scroll-padding-top: auto; scroll-snap-align: none; scroll-snap-stop: normal; scroll-snap-type: none; scrollbar-color: auto; scrollbar-gutter: auto; scrollbar-width: auto; shape-image-threshold: 0; shape-margin: 0px; shape-outside: none; shape-rendering: auto; stop-color: rgb(0, 0, 0); stop-opacity: 1; stroke: none; stroke-dasharray: none; stroke-dashoffset: 0px; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 4; stroke-opacity: 1; stroke-width: 1px; tab-size: 4; table-layout: auto; text-align: start; text-align-last: auto; text-anchor: start; text-combine-upright: none; text-decoration: rgba(48, 48, 48, 0.8); text-decoration-skip-ink: auto; text-emphasis: none rgba(48, 48, 48, 0.8); text-emphasis-position: auto; text-indent: 0px; text-justify: auto; text-orientation: mixed; text-overflow: clip; text-rendering: auto; text-shadow: none; text-transform: none; text-underline-offset: auto; text-underline-position: auto; text-wrap: wrap; top: auto; touch-action: auto; transform: none; transform-box: view-box; transform-origin: 300px 300px 0px; transform-style: flat; transition: all; translate: none; unicode-bidi: isolate; user-select: auto; vector-effect: none; vertical-align: baseline; visibility: visible; white-space: normal; width: 600px; will-change: auto; word-break: normal; word-spacing: 0px; writing-mode: horizontal-tb; x: 0px; y: 0px; z-index: auto; zoom: 1; -moz-box-align: stretch; -moz-box-direction: normal; -moz-box-flex: 0; -moz-box-ordinal-group: 1; -moz-box-orient: horizontal; -moz-box-pack: start; -moz-float-edge: content-box; -moz-force-broken-image-icon: 0; -moz-orient: inline; -moz-osx-font-smoothing: auto; -moz-text-size-adjust: auto; -moz-user-input: auto; -moz-window-dragging: default; -webkit-line-clamp: none; -webkit-text-fill-color: rgba(48, 48, 48, 0.8); -webkit-text-security: none; -webkit-text-stroke: 0px rgba(48, 48, 48, 0.8); --tw-bg-opacity: 1; --tab-radius: 0.2rem; --rounded-badge: 0.2rem; --rounded-btn: 0.2rem; --rounded-box: 0.2rem; --er: 0 100% 50%; --wa: 60 30% 50%; --su: 120 100% 25%; --in: 240 100% 50%; --b3: 0 0% 87%; --b2: 0 0% 93%; --b1: 0 0% 100%; --n: 0 0% 92%; --a: 0 0% 72%; --s: 0 0% 72%; --p: 0 0% 72%; --erc: 17 100% 90%; --wac: 58 21% 11%; --suc: 105 32% 85%; --inc: 263 100% 91%; --nc: 145 0% 18%; --ac: 145 0% 15%; --sc: 145 0% 15%; --pc: 145 0% 15%; --bc: 146 0% 19%; --nf: 0 0% 85%; --af: 0 0% 65%; --sf: 0 0% 65%; --pf: 0 0% 65%; --fbc-font-size: 13px; --fbc-secondary-text: #5B5B66; --fbc-primary-text: #15141A; --fbc-borders: 1px solid #ededf0; --fbc-transition: all .15s cubic-bezier(.07,.95,0,1); --fbc-white: #ffffff; --fbc-light-gray: #F0F0F4; --fbc-gray-20: #ededf0; --fbc-blue-70: #003eaa; --fbc-blue-60: #0060df; --rt-opacity: 0.9; --rt-color-info: #337ab7; --rt-color-warning: #f0ad4e; --rt-color-error: #be6464; --rt-color-success: #8dc572; --rt-color-dark: #222; --rt-color-white: #fff; --tab-border: 1px; --border-btn: 1px; --btn-focus-scale: 0.95; --btn-text-case: uppercase; --animation-input: .2s; --animation-btn: 0.25s; --tw-shadow-colored: 0 0 #0000;"><div style="background: rgb(1, 132, 199); border-bottom-right-radius: 115px; border-bottom-left-radius: 115px; accent-color: auto; place-content: normal center; place-items: center normal; place-self: auto; animation-composition: replace; animation: none; appearance: none; aspect-ratio: 1 / 1; backdrop-filter: none; backface-visibility: visible; background-blend-mode: normal; baseline-source: auto; block-size: 510px; border-block: 0px solid rgb(229, 231, 235); border-bottom: 0px solid rgb(229, 231, 235); border-collapse: separate; border-end-end-radius: 115px; border-end-start-radius: 115px; border-image: none; border-inline: 0px solid rgb(229, 231, 235); border-left: 0px solid rgb(229, 231, 235); border-right: 0px solid rgb(229, 231, 235); border-spacing: 0px; border-start-end-radius: 115px; border-start-start-radius: 115px; border-top: 0px solid rgb(229, 231, 235); border-top-left-radius: 115px; border-top-right-radius: 115px; bottom: auto; box-decoration-break: slice; box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.1) 0px 4px 6px -4px; box-sizing: border-box; break-after: auto; break-before: auto; break-inside: auto; caption-side: top; caret-color: rgba(48, 48, 48, 0.8); clear: none; clip: auto; clip-path: none; clip-rule: nonzero; color: rgba(48, 48, 48, 0.8); color-interpolation: srgb; color-interpolation-filters: linearrgb; color-scheme: light; columns: auto; column-fill: balance; gap: normal; column-rule: 0px none rgba(48, 48, 48, 0.8); column-span: none; contain: none; contain-intrinsic-block-size: none; contain-intrinsic-height: none; contain-intrinsic-inline-size: none; contain-intrinsic-width: none; container: none; content: normal; content-visibility: visible; counter-increment: none; counter-reset: none; counter-set: none; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: flex; dominant-baseline: auto; empty-cells: show; fill: rgb(0, 0, 0); fill-opacity: 1; fill-rule: nonzero; filter: none; flex: 0 1 auto; flex-flow: row; float: none; flood-color: rgb(0, 0, 0); flood-opacity: 1; font: 15.9px / 24px __Space_Grotesk_587f35, __Space_Grotesk_Fallback_587f35; font-palette: normal; font-synthesis: weight style small-caps position; forced-color-adjust: auto; grid: none; grid-area: auto; height: 510px; hyphenate-character: auto; hyphens: manual; image-orientation: from-image; image-rendering: auto; ime-mode: auto; inline-size: 510px; inset-block: auto; inset-inline: auto; isolation: auto; left: auto; letter-spacing: normal; lighting-color: rgb(255, 255, 255); line-break: auto; list-style: outside; margin-block: 0px; margin-bottom: 0px; margin-inline: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; marker: none; mask: none; mask-type: luminance; math-depth: 0; math-style: normal; max-block-size: none; max-height: none; max-inline-size: none; max-width: none; min-block-size: 0px; min-height: 0px; min-inline-size: 0px; min-width: 0px; mix-blend-mode: normal; object-fit: fill; object-position: 50% 50%; offset: normal; opacity: 1; order: 0; outline: rgba(48, 48, 48, 0.8) 0px; outline-offset: 0px; overflow-anchor: auto; overflow-block: hidden; overflow-clip-margin: 0px; overflow-inline: hidden; overflow-wrap: normal; overflow: hidden; overscroll-behavior-block: auto; overscroll-behavior-inline: auto; overscroll-behavior: auto; padding-block: 0px; padding-bottom: 0px; padding-inline: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px; page: auto; paint-order: normal; perspective: none; perspective-origin: 255px 255px; pointer-events: auto; position: static; print-color-adjust: economy; quotes: auto; r: 0px; resize: none; right: auto; rotate: none; ruby-align: space-around; ruby-position: alternate; rx: auto; ry: auto; scale: none; scroll-behavior: auto; scroll-margin-block: 0px; scroll-margin-bottom: 0px; scroll-margin-inline: 0px; scroll-margin-left: 0px; scroll-margin-right: 0px; scroll-margin-top: 0px; scroll-padding-block: auto; scroll-padding-bottom: auto; scroll-padding-inline: auto; scroll-padding-left: auto; scroll-padding-right: auto; scroll-padding-top: auto; scroll-snap-align: none; scroll-snap-stop: normal; scroll-snap-type: none; scrollbar-color: auto; scrollbar-gutter: auto; scrollbar-width: auto; shape-image-threshold: 0; shape-margin: 0px; shape-outside: none; shape-rendering: auto; stop-color: rgb(0, 0, 0); stop-opacity: 1; stroke: none; stroke-dasharray: none; stroke-dashoffset: 0px; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 4; stroke-opacity: 1; stroke-width: 1px; tab-size: 4; table-layout: auto; text-align: start; text-align-last: auto; text-anchor: start; text-combine-upright: none; text-decoration: rgba(48, 48, 48, 0.8); text-decoration-skip-ink: auto; text-emphasis: none rgba(48, 48, 48, 0.8); text-emphasis-position: auto; text-indent: 0px; text-justify: auto; text-orientation: mixed; text-overflow: clip; text-rendering: auto; text-shadow: none; text-transform: none; text-underline-offset: auto; text-underline-position: auto; text-wrap: wrap; top: auto; touch-action: auto; transform: none; transform-box: view-box; transform-origin: 255px 255px 0px; transform-style: flat; transition: all; translate: none; unicode-bidi: isolate; user-select: auto; vector-effect: none; vertical-align: baseline; visibility: visible; white-space: normal; width: 510px; will-change: auto; word-break: normal; word-spacing: 0px; writing-mode: horizontal-tb; x: 0px; y: 0px; z-index: auto; zoom: 1; -moz-box-align: stretch; -moz-box-direction: normal; -moz-box-flex: 0; -moz-box-ordinal-group: 1; -moz-box-orient: horizontal; -moz-box-pack: start; -moz-float-edge: content-box; -moz-force-broken-image-icon: 0; -moz-orient: inline; -moz-osx-font-smoothing: auto; -moz-text-size-adjust: auto; -moz-user-input: auto; -moz-window-dragging: default; -webkit-line-clamp: none; -webkit-text-fill-color: rgba(48, 48, 48, 0.8); -webkit-text-security: none; -webkit-text-stroke: 0px rgba(48, 48, 48, 0.8); --tw-shadow: 0 10px 15px -3px rgba(0,0,0,.1),0 4px 6px -4px rgba(0,0,0,.1); --tw-bg-opacity: 1; --tab-radius: 0.2rem; --rounded-badge: 0.2rem; --rounded-btn: 0.2rem; --rounded-box: 0.2rem; --er: 0 100% 50%; --wa: 60 30% 50%; --su: 120 100% 25%; --in: 240 100% 50%; --b3: 0 0% 87%; --b2: 0 0% 93%; --b1: 0 0% 100%; --n: 0 0% 92%; --a: 0 0% 72%; --s: 0 0% 72%; --p: 0 0% 72%; --erc: 17 100% 90%; --wac: 58 21% 11%; --suc: 105 32% 85%; --inc: 263 100% 91%; --nc: 145 0% 18%; --ac: 145 0% 15%; --sc: 145 0% 15%; --pc: 145 0% 15%; --bc: 146 0% 19%; --nf: 0 0% 85%; --af: 0 0% 65%; --sf: 0 0% 65%; --pf: 0 0% 65%; --fbc-font-size: 13px; --fbc-secondary-text: #5B5B66; --fbc-primary-text: #15141A; --fbc-borders: 1px solid #ededf0; --fbc-transition: all .15s cubic-bezier(.07,.95,0,1); --fbc-white: #ffffff; --fbc-light-gray: #F0F0F4; --fbc-gray-20: #ededf0; --fbc-blue-70: #003eaa; --fbc-blue-60: #0060df; --rt-opacity: 0.9; --rt-color-info: #337ab7; --rt-color-warning: #f0ad4e; --rt-color-error: #be6464; --rt-color-success: #8dc572; --rt-color-dark: #222; --rt-color-white: #fff; --tab-border: 1px; --border-btn: 1px; --btn-focus-scale: 0.95; --btn-text-case: uppercase; --animation-input: .2s; --animation-btn: 0.25s;" class="w-full aspect-square overflow-hidden flex justify-center items-center shadow-lg"><span style="transform: matrix(1, 0, 0, 1, 0, 0); accent-color: auto; place-content: normal; place-items: normal; place-self: auto; animation-composition: replace; animation: none; appearance: none; aspect-ratio: auto; backdrop-filter: none; backface-visibility: visible; background: none; background-blend-mode: normal; baseline-source: auto; block-size: 395px; border-block: 0px solid rgb(229, 231, 235); border-bottom: 0px solid rgb(229, 231, 235); border-bottom-left-radius: 0px; border-bottom-right-radius: 0px; border-collapse: separate; border-end-end-radius: 0px; border-end-start-radius: 0px; border-image: none; border-inline: 0px solid rgb(229, 231, 235); border-left: 0px solid rgb(229, 231, 235); border-right: 0px solid rgb(229, 231, 235); border-spacing: 0px; border-start-end-radius: 0px; border-start-start-radius: 0px; border-top: 0px solid rgb(229, 231, 235); border-top-left-radius: 0px; border-top-right-radius: 0px; bottom: auto; box-decoration-break: slice; box-shadow: none; box-sizing: border-box; break-after: auto; break-before: auto; break-inside: auto; caption-side: top; caret-color: rgba(48, 48, 48, 0.8); clear: none; clip: auto; clip-path: none; clip-rule: nonzero; color: rgba(48, 48, 48, 0.8); color-interpolation: srgb; color-interpolation-filters: linearrgb; color-scheme: light; columns: auto; column-fill: balance; gap: normal; column-rule: 0px none rgba(48, 48, 48, 0.8); column-span: none; contain: none; contain-intrinsic-block-size: none; contain-intrinsic-height: none; contain-intrinsic-inline-size: none; contain-intrinsic-width: none; container: none; content: normal; content-visibility: visible; counter-increment: none; counter-reset: none; counter-set: none; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: block; dominant-baseline: auto; empty-cells: show; fill: rgb(0, 0, 0); fill-opacity: 1; fill-rule: nonzero; filter: none; flex: 0 1 auto; flex-flow: row; float: none; flood-color: rgb(0, 0, 0); flood-opacity: 1; font: 15.9px / 24px __Space_Grotesk_587f35, __Space_Grotesk_Fallback_587f35; font-palette: normal; font-synthesis: weight style small-caps position; forced-color-adjust: auto; grid: none; grid-area: auto; height: 395px; hyphenate-character: auto; hyphens: manual; image-orientation: from-image; image-rendering: auto; ime-mode: auto; inline-size: 395px; inset-block: auto; inset-inline: auto; isolation: auto; left: auto; letter-spacing: normal; lighting-color: rgb(255, 255, 255); line-break: auto; list-style: outside; margin-block: 0px; margin-bottom: 0px; margin-inline: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; marker: none; mask: none; mask-type: luminance; math-depth: 0; math-style: normal; max-block-size: none; max-height: none; max-inline-size: none; max-width: none; min-block-size: auto; min-height: auto; min-inline-size: auto; min-width: auto; mix-blend-mode: normal; object-fit: fill; object-position: 50% 50%; offset: normal; opacity: 1; order: 0; outline: rgba(48, 48, 48, 0.8) 0px; outline-offset: 0px; overflow-anchor: auto; overflow-block: visible; overflow-clip-margin: 0px; overflow-inline: visible; overflow-wrap: normal; overflow: visible; overscroll-behavior-block: auto; overscroll-behavior-inline: auto; overscroll-behavior: auto; padding-block: 0px; padding-bottom: 0px; padding-inline: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px; page: auto; paint-order: normal; perspective: none; perspective-origin: 197.5px 197.5px; pointer-events: auto; position: static; print-color-adjust: economy; quotes: auto; r: 0px; resize: none; right: auto; rotate: none; ruby-align: space-around; ruby-position: alternate; rx: auto; ry: auto; scale: none; scroll-behavior: auto; scroll-margin-block: 0px; scroll-margin-bottom: 0px; scroll-margin-inline: 0px; scroll-margin-left: 0px; scroll-margin-right: 0px; scroll-margin-top: 0px; scroll-padding-block: auto; scroll-padding-bottom: auto; scroll-padding-inline: auto; scroll-padding-left: auto; scroll-padding-right: auto; scroll-padding-top: auto; scroll-snap-align: none; scroll-snap-stop: normal; scroll-snap-type: none; scrollbar-color: auto; scrollbar-gutter: auto; scrollbar-width: auto; shape-image-threshold: 0; shape-margin: 0px; shape-outside: none; shape-rendering: auto; stop-color: rgb(0, 0, 0); stop-opacity: 1; stroke: none; stroke-dasharray: none; stroke-dashoffset: 0px; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 4; stroke-opacity: 1; stroke-width: 1px; tab-size: 4; table-layout: auto; text-align: start; text-align-last: auto; text-anchor: start; text-combine-upright: none; text-decoration: rgba(48, 48, 48, 0.8); text-decoration-skip-ink: auto; text-emphasis: none rgba(48, 48, 48, 0.8); text-emphasis-position: auto; text-indent: 0px; text-justify: auto; text-orientation: mixed; text-overflow: clip; text-rendering: auto; text-shadow: none; text-transform: none; text-underline-offset: auto; text-underline-position: auto; text-wrap: wrap; top: auto; touch-action: auto; transform-box: view-box; transform-origin: 197.5px 197.5px 0px; transform-style: flat; transition: all; translate: none; unicode-bidi: normal; user-select: auto; vector-effect: none; vertical-align: baseline; visibility: visible; white-space: normal; width: 395px; will-change: auto; word-break: normal; word-spacing: 0px; writing-mode: horizontal-tb; x: 0px; y: 0px; z-index: auto; zoom: 1; -moz-box-align: stretch; -moz-box-direction: normal; -moz-box-flex: 0; -moz-box-ordinal-group: 1; -moz-box-orient: horizontal; -moz-box-pack: start; -moz-float-edge: content-box; -moz-force-broken-image-icon: 0; -moz-orient: inline; -moz-osx-font-smoothing: auto; -moz-text-size-adjust: auto; -moz-user-input: auto; -moz-window-dragging: default; -webkit-line-clamp: none; -webkit-text-fill-color: rgba(48, 48, 48, 0.8); -webkit-text-security: none; -webkit-text-stroke: 0px rgba(48, 48, 48, 0.8); --tw-shadow-colored: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-bg-opacity: 1; --tab-radius: 0.2rem; --rounded-badge: 0.2rem; --rounded-btn: 0.2rem; --rounded-box: 0.2rem; --er: 0 100% 50%; --wa: 60 30% 50%; --su: 120 100% 25%; --in: 240 100% 50%; --b3: 0 0% 87%; --b2: 0 0% 93%; --b1: 0 0% 100%; --n: 0 0% 92%; --a: 0 0% 72%; --s: 0 0% 72%; --p: 0 0% 72%; --erc: 17 100% 90%; --wac: 58 21% 11%; --suc: 105 32% 85%; --inc: 263 100% 91%; --nc: 145 0% 18%; --ac: 145 0% 15%; --sc: 145 0% 15%; --pc: 145 0% 15%; --bc: 146 0% 19%; --nf: 0 0% 85%; --af: 0 0% 65%; --sf: 0 0% 65%; --pf: 0 0% 65%; --fbc-font-size: 13px; --fbc-secondary-text: #5B5B66; --fbc-primary-text: #15141A; --fbc-borders: 1px solid #ededf0; --fbc-transition: all .15s cubic-bezier(.07,.95,0,1); --fbc-white: #ffffff; --fbc-light-gray: #F0F0F4; --fbc-gray-20: #ededf0; --fbc-blue-70: #003eaa; --fbc-blue-60: #0060df; --rt-opacity: 0.9; --rt-color-info: #337ab7; --rt-color-warning: #f0ad4e; --rt-color-error: #be6464; --rt-color-success: #8dc572; --rt-color-dark: #222; --rt-color-white: #fff; --tab-border: 1px; --border-btn: 1px; --btn-focus-scale: 0.95; --btn-text-case: uppercase; --animation-input: .2s; --animation-btn: 0.25s;"><svg xmlns="http://www.w3.org/2000/svg" width="395" height="395" viewBox="0 0 24 24" fill="#ffffff" stroke="rgba(255, 255, 255, 1)" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud" fill-opacity="1" style="accent-color: auto; place-content: normal; place-items: normal; place-self: auto; animation-composition: replace; animation: none; appearance: none; aspect-ratio: auto; backdrop-filter: none; backface-visibility: visible; background: none; background-blend-mode: normal; baseline-source: auto; block-size: 395px; border-block: 0px solid rgb(229, 231, 235); border-bottom: 0px solid rgb(229, 231, 235); border-bottom-left-radius: 0px; border-bottom-right-radius: 0px; border-collapse: separate; border-end-end-radius: 0px; border-end-start-radius: 0px; border-image: none; border-inline: 0px solid rgb(229, 231, 235); border-left: 0px solid rgb(229, 231, 235); border-right: 0px solid rgb(229, 231, 235); border-spacing: 0px; border-start-end-radius: 0px; border-start-start-radius: 0px; border-top: 0px solid rgb(229, 231, 235); border-top-left-radius: 0px; border-top-right-radius: 0px; bottom: auto; box-decoration-break: slice; box-shadow: none; box-sizing: border-box; break-after: auto; break-before: auto; break-inside: auto; caption-side: top; caret-color: rgba(48, 48, 48, 0.8); clear: none; clip: auto; clip-path: none; clip-rule: nonzero; color: rgba(48, 48, 48, 0.8); color-interpolation: srgb; color-interpolation-filters: linearrgb; color-scheme: light; columns: auto; column-fill: balance; gap: normal; column-rule: 0px none rgba(48, 48, 48, 0.8); column-span: none; contain: none; contain-intrinsic-block-size: none; contain-intrinsic-height: none; contain-intrinsic-inline-size: none; contain-intrinsic-width: none; container: none; content: normal; content-visibility: visible; counter-increment: none; counter-reset: none; counter-set: none; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: block; dominant-baseline: auto; empty-cells: show; fill: rgb(255, 255, 255); fill-opacity: 1; fill-rule: nonzero; filter: none; flex: 0 1 auto; flex-flow: row; float: none; flood-color: rgb(0, 0, 0); flood-opacity: 1; font: 15.9px / 24px __Space_Grotesk_587f35, __Space_Grotesk_Fallback_587f35; font-palette: normal; font-synthesis: weight style small-caps position; forced-color-adjust: auto; grid: none; grid-area: auto; height: 395px; hyphenate-character: auto; hyphens: manual; image-orientation: from-image; image-rendering: auto; ime-mode: auto; inline-size: 395px; inset-block: auto; inset-inline: auto; isolation: auto; left: auto; letter-spacing: normal; lighting-color: rgb(255, 255, 255); line-break: auto; list-style: outside; margin-block: 0px; margin-bottom: 0px; margin-inline: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; marker: none; mask: none; mask-type: luminance; math-depth: 0; math-style: normal; max-block-size: none; max-height: none; max-inline-size: none; max-width: none; min-block-size: 0px; min-height: 0px; min-inline-size: 0px; min-width: 0px; mix-blend-mode: normal; object-fit: fill; object-position: 50% 50%; offset: normal; opacity: 1; order: 0; outline: rgba(48, 48, 48, 0.8) 0px; outline-offset: 0px; overflow-anchor: auto; overflow-block: hidden; overflow-clip-margin: 0px; overflow-inline: hidden; overflow-wrap: normal; overflow: hidden; overscroll-behavior-block: auto; overscroll-behavior-inline: auto; overscroll-behavior: auto; padding-block: 0px; padding-bottom: 0px; padding-inline: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px; page: auto; paint-order: normal; perspective: none; perspective-origin: 197.5px 197.5px; pointer-events: auto; position: static; print-color-adjust: economy; quotes: auto; r: 0px; resize: none; right: auto; rotate: none; ruby-align: space-around; ruby-position: alternate; rx: auto; ry: auto; scale: none; scroll-behavior: auto; scroll-margin-block: 0px; scroll-margin-bottom: 0px; scroll-margin-inline: 0px; scroll-margin-left: 0px; scroll-margin-right: 0px; scroll-margin-top: 0px; scroll-padding-block: auto; scroll-padding-bottom: auto; scroll-padding-inline: auto; scroll-padding-left: auto; scroll-padding-right: auto; scroll-padding-top: auto; scroll-snap-align: none; scroll-snap-stop: normal; scroll-snap-type: none; scrollbar-color: auto; scrollbar-gutter: auto; scrollbar-width: auto; shape-image-threshold: 0; shape-margin: 0px; shape-outside: none; shape-rendering: auto; stop-color: rgb(0, 0, 0); stop-opacity: 1; stroke: rgb(255, 255, 255); stroke-dasharray: none; stroke-dashoffset: 0px; stroke-linecap: round; stroke-linejoin: round; stroke-miterlimit: 4; stroke-opacity: 1; stroke-width: 1px; tab-size: 4; table-layout: auto; text-align: start; text-align-last: auto; text-anchor: start; text-combine-upright: none; text-decoration: rgba(48, 48, 48, 0.8); text-decoration-skip-ink: auto; text-emphasis: none rgba(48, 48, 48, 0.8); text-emphasis-position: auto; text-indent: 0px; text-justify: auto; text-orientation: mixed; text-overflow: clip; text-rendering: auto; text-shadow: none; text-transform: none; text-underline-offset: auto; text-underline-position: auto; text-wrap: wrap; top: auto; touch-action: auto; transform: none; transform-box: view-box; transform-origin: 197.5px 197.5px 0px; transform-style: flat; transition: all; translate: none; unicode-bidi: normal; user-select: auto; vector-effect: none; vertical-align: middle; visibility: visible; white-space: normal; width: 395px; will-change: auto; word-break: normal; word-spacing: 0px; writing-mode: horizontal-tb; x: 0px; y: 0px; z-index: auto; zoom: 1; -moz-box-align: stretch; -moz-box-direction: normal; -moz-box-flex: 0; -moz-box-ordinal-group: 1; -moz-box-orient: horizontal; -moz-box-pack: start; -moz-float-edge: content-box; -moz-force-broken-image-icon: 0; -moz-orient: inline; -moz-osx-font-smoothing: auto; -moz-text-size-adjust: auto; -moz-user-input: auto; -moz-window-dragging: default; -webkit-line-clamp: none; -webkit-text-fill-color: rgba(48, 48, 48, 0.8); -webkit-text-security: none; -webkit-text-stroke: 0px rgba(48, 48, 48, 0.8); --tw-shadow-colored: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-bg-opacity: 1; --tab-radius: 0.2rem; --rounded-badge: 0.2rem; --rounded-btn: 0.2rem; --rounded-box: 0.2rem; --er: 0 100% 50%; --wa: 60 30% 50%; --su: 120 100% 25%; --in: 240 100% 50%; --b3: 0 0% 87%; --b2: 0 0% 93%; --b1: 0 0% 100%; --n: 0 0% 92%; --a: 0 0% 72%; --s: 0 0% 72%; --p: 0 0% 72%; --erc: 17 100% 90%; --wac: 58 21% 11%; --suc: 105 32% 85%; --inc: 263 100% 91%; --nc: 145 0% 18%; --ac: 145 0% 15%; --sc: 145 0% 15%; --pc: 145 0% 15%; --bc: 146 0% 19%; --nf: 0 0% 85%; --af: 0 0% 65%; --sf: 0 0% 65%; --pf: 0 0% 65%; --fbc-font-size: 13px; --fbc-secondary-text: #5B5B66; --fbc-primary-text: #15141A; --fbc-borders: 1px solid #ededf0; --fbc-transition: all .15s cubic-bezier(.07,.95,0,1); --fbc-white: #ffffff; --fbc-light-gray: #F0F0F4; --fbc-gray-20: #ededf0; --fbc-blue-70: #003eaa; --fbc-blue-60: #0060df; --rt-opacity: 0.9; --rt-color-info: #337ab7; --rt-color-warning: #f0ad4e; --rt-color-error: #be6464; --rt-color-success: #8dc572; --rt-color-dark: #222; --rt-color-white: #fff; --tab-border: 1px; --border-btn: 1px; --btn-focus-scale: 0.95; --btn-text-case: uppercase; --animation-input: .2s; --animation-btn: 0.25s;"><path d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z" style="accent-color: auto; place-content: normal; place-items: normal; place-self: auto; animation-composition: replace; animation: none; appearance: none; aspect-ratio: auto; backdrop-filter: none; backface-visibility: visible; background: none; background-blend-mode: normal; baseline-source: auto; block-size: auto; border-block: 0px solid rgb(229, 231, 235); border-bottom: 0px solid rgb(229, 231, 235); border-bottom-left-radius: 0px; border-bottom-right-radius: 0px; border-collapse: separate; border-end-end-radius: 0px; border-end-start-radius: 0px; border-image: none; border-inline: 0px solid rgb(229, 231, 235); border-left: 0px solid rgb(229, 231, 235); border-right: 0px solid rgb(229, 231, 235); border-spacing: 0px; border-start-end-radius: 0px; border-start-start-radius: 0px; border-top: 0px solid rgb(229, 231, 235); border-top-left-radius: 0px; border-top-right-radius: 0px; bottom: auto; box-decoration-break: slice; box-shadow: none; box-sizing: border-box; break-after: auto; break-before: auto; break-inside: auto; caption-side: top; caret-color: rgba(48, 48, 48, 0.8); clear: none; clip: auto; clip-path: none; clip-rule: nonzero; color: rgba(48, 48, 48, 0.8); color-interpolation: srgb; color-interpolation-filters: linearrgb; color-scheme: light; columns: auto; column-fill: balance; gap: normal; column-rule: 0px none rgba(48, 48, 48, 0.8); column-span: none; contain: none; contain-intrinsic-block-size: none; contain-intrinsic-height: none; contain-intrinsic-inline-size: none; contain-intrinsic-width: none; container: none; content: normal; content-visibility: visible; counter-increment: none; counter-reset: none; counter-set: none; cursor: auto; cx: 0px; cy: 0px; direction: ltr; display: inline; dominant-baseline: auto; empty-cells: show; fill: rgb(255, 255, 255); fill-opacity: 1; fill-rule: nonzero; filter: none; flex: 0 1 auto; flex-flow: row; float: none; flood-color: rgb(0, 0, 0); flood-opacity: 1; font: 15.9px / 24px __Space_Grotesk_587f35, __Space_Grotesk_Fallback_587f35; font-palette: normal; font-synthesis: weight style small-caps position; forced-color-adjust: auto; grid: none; grid-area: auto; height: auto; hyphenate-character: auto; hyphens: manual; image-orientation: from-image; image-rendering: auto; ime-mode: auto; inline-size: auto; inset-block: auto; inset-inline: auto; isolation: auto; left: auto; letter-spacing: normal; lighting-color: rgb(255, 255, 255); line-break: auto; list-style: outside; margin-block: 0px; margin-bottom: 0px; margin-inline: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; marker: none; mask: none; mask-type: luminance; math-depth: 0; math-style: normal; max-block-size: none; max-height: none; max-inline-size: none; max-width: none; min-block-size: 0px; min-height: 0px; min-inline-size: 0px; min-width: 0px; mix-blend-mode: normal; object-fit: fill; object-position: 50% 50%; offset: normal; opacity: 1; order: 0; outline: rgba(48, 48, 48, 0.8) 0px; outline-offset: 0px; overflow-anchor: auto; overflow-block: visible; overflow-clip-margin: 0px; overflow-inline: visible; overflow-wrap: normal; overflow: visible; overscroll-behavior-block: auto; overscroll-behavior-inline: auto; overscroll-behavior: auto; padding-block: 0px; padding-bottom: 0px; padding-inline: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px; page: auto; paint-order: normal; perspective: none; perspective-origin: 12px 12px; pointer-events: auto; position: static; print-color-adjust: economy; quotes: auto; r: 0px; resize: none; right: auto; rotate: none; ruby-align: space-around; ruby-position: alternate; rx: auto; ry: auto; scale: none; scroll-behavior: auto; scroll-margin-block: 0px; scroll-margin-bottom: 0px; scroll-margin-inline: 0px; scroll-margin-left: 0px; scroll-margin-right: 0px; scroll-margin-top: 0px; scroll-padding-block: auto; scroll-padding-bottom: auto; scroll-padding-inline: auto; scroll-padding-left: auto; scroll-padding-right: auto; scroll-padding-top: auto; scroll-snap-align: none; scroll-snap-stop: normal; scroll-snap-type: none; scrollbar-color: auto; scrollbar-gutter: auto; scrollbar-width: auto; shape-image-threshold: 0; shape-margin: 0px; shape-outside: none; shape-rendering: auto; stop-color: rgb(0, 0, 0); stop-opacity: 1; stroke: rgb(255, 255, 255); stroke-dasharray: none; stroke-dashoffset: 0px; stroke-linecap: round; stroke-linejoin: round; stroke-miterlimit: 4; stroke-opacity: 1; stroke-width: 1px; tab-size: 4; table-layout: auto; text-align: start; text-align-last: auto; text-anchor: start; text-combine-upright: none; text-decoration: rgba(48, 48, 48, 0.8); text-decoration-skip-ink: auto; text-emphasis: none rgba(48, 48, 48, 0.8); text-emphasis-position: auto; text-indent: 0px; text-justify: auto; text-orientation: mixed; text-overflow: clip; text-rendering: auto; text-shadow: none; text-transform: none; text-underline-offset: auto; text-underline-position: auto; text-wrap: wrap; top: auto; touch-action: auto; transform: none; transform-box: view-box; transform-origin: 0px 0px 0px; transform-style: flat; transition: all; translate: none; unicode-bidi: normal; user-select: none; vector-effect: none; vertical-align: baseline; visibility: visible; white-space: normal; width: auto; will-change: auto; word-break: normal; word-spacing: 0px; writing-mode: horizontal-tb; x: 0px; y: 0px; z-index: auto; zoom: 1; -moz-box-align: stretch; -moz-box-direction: normal; -moz-box-flex: 0; -moz-box-ordinal-group: 1; -moz-box-orient: horizontal; -moz-box-pack: start; -moz-float-edge: content-box; -moz-force-broken-image-icon: 0; -moz-orient: inline; -moz-osx-font-smoothing: auto; -moz-text-size-adjust: auto; -moz-user-input: auto; -moz-window-dragging: default; -webkit-line-clamp: none; -webkit-text-fill-color: rgba(48, 48, 48, 0.8); -webkit-text-security: none; -webkit-text-stroke: 0px rgba(48, 48, 48, 0.8); --tw-shadow-colored: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-bg-opacity: 1; --tab-radius: 0.2rem; --rounded-badge: 0.2rem; --rounded-btn: 0.2rem; --rounded-box: 0.2rem; --er: 0 100% 50%; --wa: 60 30% 50%; --su: 120 100% 25%; --in: 240 100% 50%; --b3: 0 0% 87%; --b2: 0 0% 93%; --b1: 0 0% 100%; --n: 0 0% 92%; --a: 0 0% 72%; --s: 0 0% 72%; --p: 0 0% 72%; --erc: 17 100% 90%; --wac: 58 21% 11%; --suc: 105 32% 85%; --inc: 263 100% 91%; --nc: 145 0% 18%; --ac: 145 0% 15%; --sc: 145 0% 15%; --pc: 145 0% 15%; --bc: 146 0% 19%; --nf: 0 0% 85%; --af: 0 0% 65%; --sf: 0 0% 65%; --pf: 0 0% 65%; --fbc-font-size: 13px; --fbc-secondary-text: #5B5B66; --fbc-primary-text: #15141A; --fbc-borders: 1px solid #ededf0; --fbc-transition: all .15s cubic-bezier(.07,.95,0,1); --fbc-white: #ffffff; --fbc-light-gray: #F0F0F4; --fbc-gray-20: #ededf0; --fbc-blue-70: #003eaa; --fbc-blue-60: #0060df; --rt-opacity: 0.9; --rt-color-info: #337ab7; --rt-color-warning: #f0ad4e; --rt-color-error: #be6464; --rt-color-success: #8dc572; --rt-color-dark: #222; --rt-color-white: #fff; --tab-border: 1px; --border-btn: 1px; --btn-focus-scale: 0.95; --btn-text-case: uppercase; --animation-input: .2s; --animation-btn: 0.25s;"/></svg></span></div></div></foreignObject></svg>