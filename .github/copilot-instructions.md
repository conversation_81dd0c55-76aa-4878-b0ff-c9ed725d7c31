## Instructions for Copilot
- You are an guru developer, you are an expert in PHP, Laravel, Tailwind CSS v4, and Filament v4.
- You are an expert in writing clean, readable, beautiful and maintainable code.
- We are using Filament v4 to build the dashboard.

## App styles
- Use Tailwind CSS for styling.
- Default color is `primary` (the colors.sky) from tailwind
- Use the `bg-primary` class for background color.
- Use the `text-primary` class for text color.

## Image skymage exmaple
When using example image skymage, use the following format: If the original image is `https://example.com/image.png`, the skymage image should be `https://demo.skymage/net/v1/example.com/image.png`.

## About Skymage CDN
- The website is https://skymage.daudau.cc, so if there is any link to the website, please use this link.

## Write blog posts
- Use the `resources/blog` folder for blog posts.
- Blog must be about Image optimization, skymage, or related topics.
- Blog post must be SEO friendly, abtract and must let users want to read more.
- The post file must be in markdown format, file name in format: `Y-d-d-title-of-the-post.md`.
- Blog post must has frontmatter with 3 fields: `title`, `description`, `featured_image` (images.unsplash.com or images.pexels.com images which are related to the post).
- The featured image of blog post should not includes any query string, it should be a direct link to the image.
- The blog content should not has h1 (#) because we'll use the title from frontmatter as the title.
- When be asking to write a blog post, pls check previous blog posts in `resources/blog` to avoid duplication.

## Code style
- Use PHP 8.3 syntax, use much typed system as possible.
- Use PSR-12 coding style.
- Write clean and readable code.

## Laravel
- PHP version is 8.3
- Always try to use the blade componets.
- Use the heroicons package for icons as much as possible.
- Always use the __("key") function for translations.
- Use Pest PHP for testing
- Avoid using $fillable in models
- Never use enum for column type in migrations, use string instead.

## About updating the llms.txt

- The llms.txt must be generated from `app:generate-llms` command. Never directly edit the llms.txt file. Always ensure to run the command after making changes to the relevant files to keep the documentation up to date.
