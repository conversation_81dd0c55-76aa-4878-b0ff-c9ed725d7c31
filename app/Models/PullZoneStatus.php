<?php

namespace App\Models;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum PullZoneStatus: string implements HasLabel, HasIcon, HasColor
{
    case Pending = 'pending';
    case Provisioning = 'provisioning';
    case Active = 'active';
    case Inactive = 'inactive';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Pending => __('Pending'),
            self::Provisioning => __('Provisioning'),
            self::Active => __('Active'),
            self::Inactive => __('Inactive'),
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::Pending, self::Provisioning => 'heroicon-o-clock',
            self::Active => 'heroicon-o-check-circle',
            self::Inactive => 'heroicon-o-x-circle',
        };
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::Pending => 'gray',
            self::Provisioning => 'info',
            self::Active => 'success',
            self::Inactive => 'warning',
        };
    }
}
