<?php

namespace App\Models;

use App\Services\ExternalStorageDriverFactory;
use Exception;
use Illuminate\Support\Facades\Log;
use App\Enums\ExternalStorageDriver;
use App\Enums\ExternalStorageStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExternalStorage extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'driver' => ExternalStorageDriver::class,
            'configuration' => 'array', // TODO: Change to 'encrypted:array' in production
            'status' => ExternalStorageStatus::class,
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getDriverDisplayName(): string
    {
        return $this->driver->getLabel();
    }

    public function testConnection(): array
    {
        try {
            $driver = ExternalStorageDriverFactory::create($this);
            $result = $driver->isValid();

            $status = $result['success'] ? ExternalStorageStatus::Connected : ExternalStorageStatus::Disconnected;
            $this->update(['status' => $status]);

            return $result;
        } catch (Exception $e) {
            $this->update(['status' => ExternalStorageStatus::Disconnected]);

            Log::error('External storage connection test failed', [
                'error' => $e->getMessage(),
                'storage_id' => $this->id,
                'driver' => $this->driver->value,
            ]);

            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ];
        }
    }

    protected static function booted(): void
    {
        static::creating(function (ExternalStorage $storage) {
            $storage->status = ExternalStorageStatus::Disconnected;
        });
    }
}
