<?php

namespace App\Models;

use Exception;
use App\Billing\Billable;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable implements FilamentUser
{
    use HasFactory, Notifiable, Billable;

    protected $guarded = ['id'];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password'          => 'hashed',
        ];
    }

    public function pullZone(): HasOne
    {
        return $this->hasOne(PullZone::class);
    }

    public function externalStorages(): HasMany
    {
        return $this->hasMany(ExternalStorage::class);
    }

    /**
     * @throws Exception
     */
    public function canAccessPanel(Panel $panel): bool
    {
        return match ($panel->getId()) {
            'dashboard' => true,
            'backoffice' => $this->isAdmin(),
            default => false,
        };
    }

    public function isAdmin(): bool
    {
        return $this->email === '<EMAIL>';
    }

    public function bandwidthLimitInGb(): ?int
    {
        if ($this->onGenericTrial()) {
            return 50;
        }

        return $this->plan()->options['bandwidth_limit_in_gb'] ?? 0;
    }
}
