<?php

namespace App\Console\Commands;

use Filament\Http\Middleware\Authenticate;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class GenerateSitemap extends Command
{
    protected $signature = 'app:generate-sitemap {--path=sitemap.xml : The path where the sitemap will be saved}';

    protected $description = 'Generate sitemap for static pages';

    public function handle(): int
    {
        $this->info('Generating sitemap for static pages...');

        $baseUrl = config('app.url');
        $excludedPatterns = [
            'api/*',
            'dashboard/*',
            'backoffice/*',
            'horizon/*',
            'livewire/*',
            'login',
            'register',
            'forgot-password',
            'reset-password/*',
            'email/*',
            'verify-email/*',
            'confirm-password',
            'logout',
        ];

        // Get the absolute path to web.php
        $webRoutesPath = base_path('routes/web.php');

        $urls = collect(Route::getRoutes())
            ->filter(function ($route) use ($excludedPatterns, $webRoutesPath) {
                // Only get GET routes that don't have parameters
                if (!in_array('GET', $route->methods()) || $route->getActionName() === 'Closure') {
                    return false;
                }

                // Skip routes with auth middleware
                $middleware = $route->middleware();

                // Remove the debug dump
                // Check for Laravel auth or Filament auth middleware
                if (in_array('auth', $middleware) ||
                    in_array('auth:sanctum', $middleware) ||
                    in_array(Authenticate::class, $middleware) ||
                    Str::contains(implode('|', $middleware), 'Filament\Http\Middleware\Authenticate')) {
                    return false;
                }

                // Only include routes from web.php
                $action = $route->getAction();
                if (!isset($action['controller']) && !isset($action['uses'])) {
                    // If there's no controller or uses key, check if we can find the file
                    if (isset($action['file'])) {
                        if ($action['file'] !== $webRoutesPath) {
                            return false;
                        }
                    } else {
                        // No way to determine where this route came from, skip it
                        return false;
                    }
                }

                $uri = $route->uri();

                // Skip routes with parameters
                if (preg_match('/{.*}/', $uri)) {
                    return false;
                }

                // Skip excluded patterns
                foreach ($excludedPatterns as $pattern) {
                    if (Str::is($pattern, $uri)) {
                        return false;
                    }
                }

                // Double check for backoffice routes since the pattern matching might miss them
                if (Str::startsWith($uri, 'backoffice')) {
                    return false;
                }

                return true;
            })
            ->map(function ($route) use ($baseUrl) {
                $uri = $route->uri() === '/' ? '' : '/' . $route->uri();
                return [
                    'loc' => $baseUrl . $uri,
                    'lastmod' => now()->toAtomString(),
                    'priority' => $uri === '' ? '1.0' : '0.8',
                    'changefreq' => 'weekly',
                ];
            })
            ->values();

        $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1" xmlns:video="http://www.google.com/schemas/sitemap-video/1.1" xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">' . PHP_EOL;

        foreach ($urls as $url) {
            $sitemap .= '  <url>' . PHP_EOL;
            $sitemap .= '    <loc>' . $url['loc'] . '</loc>' . PHP_EOL;
            $sitemap .= '    <lastmod>' . $url['lastmod'] . '</lastmod>' . PHP_EOL;
            $sitemap .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . PHP_EOL;
            $sitemap .= '    <priority>' . $url['priority'] . '</priority>' . PHP_EOL;
            $sitemap .= '  </url>' . PHP_EOL; // Added the missing closing tag
        }

        $sitemap .= '</urlset>';

        $outputPath = $this->option('path');
        File::put(public_path($outputPath), $sitemap);

        $this->info('Sitemap generated successfully: ' . public_path($outputPath));

        return Command::SUCCESS;
    }
}
