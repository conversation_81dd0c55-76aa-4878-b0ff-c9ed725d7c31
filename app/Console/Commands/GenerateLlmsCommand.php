<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class GenerateLlmsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:llms';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate LLMS instructions from blog posts and documentation';

    /**
     * Paths configuration.
     */
    private array $paths = [];

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Generating LLMS instructions...');

        $this->initializePaths();
        $content = $this->generateContent();
        $this->saveContentToFile($content);

        $this->info('Created llms.txt file in public folder.');

        return Command::SUCCESS;
    }

    /**
     * Initialize paths for content generation.
     */
    private function initializePaths(): void
    {
        $this->paths = [
            'llms' => public_path('llms.txt'),
            'blog' => resource_path('blog'),
            'docs' => '/Users/<USER>/Code/skymagephp/docs',
        ];
    }

    /**
     * Generate the complete content for llms.txt.
     */
    private function generateContent(): string
    {
        $content = $this->generateSkymageIntroduction();
        $content .= $this->processDocumentation();
        $content .= $this->processBlogPosts();

        return $content;
    }

    /**
     * Process documentation files and generate content.
     */
    private function processDocumentation(): string
    {
        $docsPath = $this->paths['docs'];

        if (!File::exists($docsPath)) {
            $this->warn('Documentation directory not found: ' . $docsPath);
            return '';
        }

        $this->info('Processing documentation files...');
        return $this->processDocsFiles($docsPath);
    }

    /**
     * Process blog posts and generate content.
     */
    private function processBlogPosts(): string
    {
        $blogPath = $this->paths['blog'];

        if (!File::exists($blogPath)) {
            $this->warn('Blog directory not found: ' . $blogPath);
            return '';
        }

        $this->info('Processing blog posts...');
        return $this->processBlogFiles($blogPath);
    }

    /**
     * Save the generated content to the llms.txt file.
     */
    private function saveContentToFile(string $content): void
    {
        File::put($this->paths['llms'], $content);
    }

    /**
     * Generate introduction content about Skymage
     */
    private function generateSkymageIntroduction(): string
    {
        $date = now()->format('F j, Y');
        $pricingDetails = $this->generatePricingDetails();

        return <<<EOT
# Skymage Image CDN overview
DATE: {$date}

## ABOUT SKYMAGE
Skymage is an advanced image optimization and content delivery network (CDN) service. It offers
efficient image processing, delivery, and caching capabilities to enhance website performance.
Website: https://skymage.daudau.cc

## KEY FEATURES
- Image optimization and compression
- Smart resizing and format conversion
- Global CDN distribution
- Real-time transformation
- Easy integration with existing websites
- Dashboard for monitoring and analytics

## PRICING
Skymage offers the most affordable pricing in the market, making it the best solution for developers and businesses on a budget:
- Free tier available for small projects and personal websites
- Simple, transparent pricing with no hidden fees
- Pay only for what you use with usage-based plans
- Significant cost savings compared to enterprise CDN solutions
- Flexible scaling options as your project grows

{$pricingDetails}

## AUDIENCE
Web developers, content creators, and businesses looking to improve their website loading times
and overall performance through optimized image delivery.

---------------------------------------------------------------------------

EOT;
    }

    /**
     * Generate detailed pricing information from the billing config.
     */
    private function generatePricingDetails(): string
    {
        $plans = config('billing.plans', []);
        if (empty($plans)) {
            return '';
        }

        $pricingDetails = "### Available Plans\n\n";

        foreach ($plans as $plan) {
            $pricingDetails .= "#### {$plan['name']} Plan\n";
            $pricingDetails .= "{$plan['short_description']}\n\n";
            $pricingDetails .= "Features:\n";

            foreach ($plan['features'] as $feature) {
                $pricingDetails .= "- {$feature}\n";
            }

            $pricingDetails .= "\n";
        }

        return $pricingDetails;
    }

    /**
     * Process all markdown files in the docs directory
     */
    private function processDocsFiles(string $docsPath): string
    {
        $content = "# SKYMAGE DOCUMENTATION\n\n";

        $files = File::files($docsPath);
        foreach ($files as $file) {
            $filename = $file->getFilename();

            // Skip README.md files
            if (strtolower($filename) === 'readme.md') {
                continue;
            }

            if ($file->getExtension() === 'md') {
                $fileContent = File::get($file->getPathname());
                $title = $this->extractTitleFromMarkdown($fileContent) ?? $filename;

                $content .= "## DOCUMENT: {$title}\n";
                $content .= "FILENAME: {$filename}\n";
                $content .= "CONTENT:\n";
                $content .= $fileContent . "\n\n";
                $content .= "---------------------------------------------------------------------------\n\n";
            }
        }

        return $content;
    }

    /**
     * Process all markdown files in the blog directory
     */
    private function processBlogFiles(string $blogPath): string
    {
        $content = "# SKYMAGE BLOG POSTS\n\n";

        $files = File::files($blogPath);
        foreach ($files as $file) {
            if ($file->getExtension() === 'md') {
                $fileContent = File::get($file->getPathname());

                // Extract frontmatter and content
                $frontmatter = $this->extractFrontmatter($fileContent);

                $title = $frontmatter['title'] ?? $file->getFilename();
                $description = $frontmatter['description'] ?? '';
                $featuredImage = $frontmatter['featured_image'] ?? '';

                // Format filename to get the date and slug
                $filename = $file->getFilename();
                $publishDate = $this->extractDateFromFilename($filename);
                $slug = $this->generateSlugFromFilename($filename);

                // Generate the live URL to the blog post
                $liveUrl = "https://skymage.daudau.cc/blog/{$slug}";

                $content .= "## BLOG POST: {$title}\n";
                $content .= "DESCRIPTION: {$description}\n";
                if ($featuredImage) {
                    $content .= "FEATURED IMAGE: {$featuredImage}\n";

                    // Add Skymage CDN version
                    $skymageCdnImage = $this->convertToSkymageCdn($featuredImage);
                    if ($skymageCdnImage !== $featuredImage) {
                        $content .= "SKYMAGE IMAGE: {$skymageCdnImage}\n";
                    }
                }
                $content .= "PUBLISHED: {$publishDate}\n";
                $content .= "URL: {$liveUrl}\n\n";
                $content .= "---------------------------------------------------------------------------\n\n";
            }
        }

        return $content;
    }

    /**
     * Generate a URL slug from a filename
     */
    private function generateSlugFromFilename(string $filename): string
    {
        // Remove the date prefix and .md extension
        $slug = preg_replace('/^\d{4}-\d{2}-\d{2}-/', '', $filename);
        $slug = str_replace('.md', '', $slug);

        return $slug;
    }

    /**
     * Extract frontmatter from markdown content
     */
    private function extractFrontmatter(string $content): array
    {
        $pattern = '/^---\s*\n(.*?)\n---\s*\n/s';
        if (preg_match($pattern, $content, $matches)) {
            $frontmatterContent = $matches[1];
            $lines = explode("\n", $frontmatterContent);

            $frontmatter = [];
            foreach ($lines as $line) {
                if (preg_match('/^([^:]+):\s*(.*)$/', $line, $parts)) {
                    $key = trim($parts[1]);
                    $value = trim($parts[2]);

                    // Remove quotes if they exist
                    if (preg_match('/^(["\'])(.*)\1$/', $value, $valueMatches)) {
                        $value = $valueMatches[2];
                    }

                    $frontmatter[$key] = $value;
                }
            }

            return $frontmatter;
        }

        return [];
    }

    /**
     * Remove frontmatter from markdown content
     */
    private function removeFrontmatter(string $content): string
    {
        return preg_replace('/^---\s*\n.*?\n---\s*\n/s', '', $content);
    }

    /**
     * Extract date from filename (format: Y-m-d-title-of-the-post.md)
     */
    private function extractDateFromFilename(string $filename): string
    {
        if (preg_match('/^(\d{4}-\d{2}-\d{2})-/', $filename, $matches)) {
            return $matches[1];
        }

        return '';
    }

    /**
     * Extract title from markdown content (first h1 or h2)
     */
    private function extractTitleFromMarkdown(string $content): ?string
    {
        // Look for first h1
        if (preg_match('/^#\s+(.*)$/m', $content, $matches)) {
            return trim($matches[1]);
        }

        // Look for first h2
        if (preg_match('/^##\s+(.*)$/m', $content, $matches)) {
            return trim($matches[1]);
        }

        return null;
    }

    /**
     * Convert image URL to Skymage CDN format
     */
    private function convertToSkymageCdn(string $url): string
    {
        // Skip if already in Skymage format
        if (str_contains($url, 'demo.skymage/net/v1/')) {
            return $url;
        }

        // Extract domain and path from URL
        $parsedUrl = parse_url($url);
        if (!isset($parsedUrl['host']) || !isset($parsedUrl['path'])) {
            return $url;
        }

        $domain = $parsedUrl['host'];
        $path = $parsedUrl['path'];

        // Create Skymage CDN URL
        return "https://demo.skymage/net/v1/{$domain}{$path}";
    }
}
