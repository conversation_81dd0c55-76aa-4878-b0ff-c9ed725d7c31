<?php

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\URL;
use Symfony\Component\Yaml\Yaml;

class GenerateBlogSitemapCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-blog-sitemap';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate a sitemap for blog posts';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating blog sitemap...');

        $blogPosts = $this->getBlogPosts();

        if (empty($blogPosts)) {
            $this->warn('No blog posts found.');
            return 1;
        }

        $xml = $this->generateSitemapXml($blogPosts);

        $path = public_path('blog.xml');
        File::put($path, $xml);

        $this->info('Blog sitemap generated successfully at: ' . $path);
        $this->info('Total blog posts in sitemap: ' . count($blogPosts));

        return 0;
    }

    /**
     * Get all blog posts
     */
    protected function getBlogPosts()
    {
        $blogDir = resource_path('blog');
        $files = File::glob($blogDir . '/*.md');

        $posts = [];
        foreach ($files as $file) {
            $fileName = basename($file);

            // Skip files that don't match our naming convention
            if (!preg_match('/^(\d{4}-\d{2}-\d{2})-(.+)\.md$/', $fileName, $matches)) {
                continue;
            }

            $datePart = $matches[1];
            $slug = $matches[2];

            try {
                $date = Carbon::createFromFormat('Y-m-d', $datePart);
            } catch (Exception $e) {
                continue; // Skip files with invalid dates
            }

            // Get last modified time
            $lastMod = Carbon::createFromTimestamp(File::lastModified($file));

            $posts[] = [
                'slug' => $slug,
                'date' => $date,
                'lastmod' => $lastMod
            ];
        }

        return collect($posts)->sortByDesc('date')->values()->all();
    }

    /**
     * Generate sitemap XML content
     */
    protected function generateSitemapXml($posts)
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . PHP_EOL;

        foreach ($posts as $post) {
            $url = URL::route('blog.show', ['slug' => $post['slug']], true);

            $xml .= '  <url>' . PHP_EOL;
            $xml .= '    <loc>' . $url . '</loc>' . PHP_EOL;
            $xml .= '    <lastmod>' . $post['lastmod']->format('Y-m-d\TH:i:sP') . '</lastmod>' . PHP_EOL;
            $xml .= '    <changefreq>monthly</changefreq>' . PHP_EOL;
            $xml .= '    <priority>0.8</priority>' . PHP_EOL;
            $xml .= '  </url>' . PHP_EOL;
        }

        $xml .= '</urlset>';

        return $xml;
    }
}
