<?php

namespace App\Filament\Pages;

use Filament\Support\Enums\Size;
use App\Filament\Widgets\BillingWidget;
use App\Http\Middleware\VerifyBillableSubscribed;
use Filament\Actions\Action;
use Filament\Pages\Page;
use App\Models\User;
use Filament\Notifications\Notification;

class Billing extends Page
{
    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-credit-card';

    protected string $view = 'filament.pages.billing';

    protected static ?int $navigationSort = 50;

    protected static string|\UnitEnum|null $navigationGroup = 'Others';

    protected static string|array $withoutRouteMiddleware = [
        VerifyBillableSubscribed::class,
    ];

    public function getHeaderActions(): array
    {
        /**
         * @var User $user
         **/
        $user = auth()->user();

        if ($user->onGenericTrial() || !$user->subscribed()) {
            return [];
        }


        return [
            Action::make('Cancel subscription')
                ->icon('heroicon-o-x-mark')
                ->color('gray')
                ->size(Size::ExtraSmall)
                ->requiresConfirmation('Are you sure you want to cancel your subscription?')
                ->modalIconColor('danger')
                ->modalDescription('You will not be able to use our service after cancelling your subscription after the current billing cycle.')

                ->action(function ()  use ($user) {
                    $user->subscription()->cancel();

                    Notification::make()
                        ->title('Subscription cancelled')
                        ->success()
                        ->send();
                }),
        ];
    }
}
