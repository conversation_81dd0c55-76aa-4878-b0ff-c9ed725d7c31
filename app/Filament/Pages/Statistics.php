<?php

namespace App\Filament\Pages;

use BackedEnum;
use Carbon\Carbon;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use UnitEnum;

class Statistics extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationLabel = 'Statistics';

    protected static ?string $title = 'Statistics';

    protected string $view = 'filament.pages.statistics';

    protected static string | null | BackedEnum $navigationIcon = Heroicon::OutlinedChartBar;

    protected static string|UnitEnum|null $navigationGroup = 'Analytics';

    public ?array $data = [];

    public function mount(): void
    {
        // Get date range from URL parameters or use default
        $dateRange = request('dateRange', 'last_7_days');

        $this->form->fill([
            'dateRange' => $dateRange,
        ]);
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('dateRange')
                    ->label(__('Date Range'))
                    ->options([
                        'last_7_days' => __('Last 7 days'),
                        'last_30_days' => __('Last 30 days'),
                        'month_to_date' => __('Month to date'),
                        'week_to_date' => __('Week to date'),
                    ])
                    ->default('last_7_days')
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn($state) => $this->updateDateFilters()),
            ])
            ->columns(1)
            ->statePath('data');
    }

    public function updateDateFilters(): void
    {
        $dateRange = $this->data['dateRange'] ?? 'last_7_days';
        [$dateFrom, $dateTo] = $this->getDateRangeFromSelection($dateRange);

        // Update URL without redirect for better UX
        $this->js("window.history.replaceState({}, '', '?' + new URLSearchParams({dateRange: '{$dateRange}'}).toString())");

        // Dispatch browser event for Alpine.js overlays
        $this->js("window.dispatchEvent(new CustomEvent('date-filters-updated'))");

        // Dispatch event to refresh widgets
        $this->dispatch('dateFiltersUpdated', [
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
    }

    protected function getFormActions(): array
    {
        return [];
    }

    public function getFilters(): array
    {
        $dateRange = $this->data['dateRange'] ?? 'last_7_days';
        [$dateFrom, $dateTo] = $this->getDateRangeFromSelection($dateRange);

        return [
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'dateRange' => $dateRange,
        ];
    }

    private function getDateRangeFromSelection(string $selection): array
    {
        $now = Carbon::now();

        return match ($selection) {
            'last_7_days' => [
                $now->copy()->subDays(7)->format('Y-m-d'),
                $now->format('Y-m-d')
            ],
            'last_30_days' => [
                $now->copy()->subDays(30)->format('Y-m-d'),
                $now->format('Y-m-d')
            ],
            'month_to_date' => [
                $now->copy()->startOfMonth()->format('Y-m-d'),
                $now->format('Y-m-d')
            ],
            'week_to_date' => [
                $now->copy()->startOfWeek()->format('Y-m-d'),
                $now->format('Y-m-d')
            ],
            default => [
                $now->copy()->subDays(7)->format('Y-m-d'),
                $now->format('Y-m-d')
            ]
        };
    }
}
