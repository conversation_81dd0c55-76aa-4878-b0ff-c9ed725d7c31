<?php

namespace App\Filament\Pages;

use BackedEnum;
use Carbon\Carbon;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use UnitEnum;

class Statistics extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationLabel = 'Statistics';

    protected static ?string $title = 'Statistics';

    protected string $view = 'filament.pages.statistics';

    protected static string | null | BackedEnum $navigationIcon = Heroicon::OutlinedChartBar;

    protected static string|UnitEnum|null $navigationGroup = 'Analytics';

    public ?array $data = [];

    public function mount(): void
    {
        // Get dates from URL parameters or use defaults
        $dateFrom = request('dateFrom', Carbon::now()->subDays(7)->format('Y-m-d'));
        $dateTo = request('dateTo', Carbon::now()->format('Y-m-d'));

        $this->form->fill([
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('dateRange')
                    ->label(__('Date Range'))
                    ->options([
                        'last_7_days' => __('Last 7 days'),
                        'last_30_days' => __('Last 30 days'),
                        'month_to_date' => __('Month to date'),
                        'week_to_date' => __('Week to date'),
                    ])
                    ->placeholder(__('Select a date range'))
                    ->live()
                    ->afterStateUpdated(fn($state) => $this->applyDateRange($state)),
                DatePicker::make('dateFrom')
                    ->label(__('From'))
                    ->required()
                    ->live()
                    ->native(false)
                    ->closeOnDateSelection()
                    ->afterStateUpdated(fn($state) => $this->updateDateFilters())
                    ->hidden(),
                DatePicker::make('dateTo')
                    ->label(__('To'))
                    ->required()
                    ->native(false)
                    ->closeOnDateSelection()
                    ->maxDate(Carbon::now())
                    ->live()
                    ->afterStateUpdated(fn($state) => $this->updateDateFilters())
                    ->hidden(),
            ])
            ->columns(1)
            ->statePath('data');
    }

    public function applyDateRange($state): void
    {
        if (!$state) {
            return;
        }

        [$dateFrom, $dateTo] = $this->getDateRangeFromSelection($state);

        $this->form->fill([
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'dateRange' => $state, // Keep the selection
        ]);

        $this->updateDateFilters();
    }

    public function updateDateFilters(): void
    {
        // Update URL parameters and refresh widgets
        $dateFrom = $this->data['dateFrom'] ?? Carbon::now()->subDays(7)->format('Y-m-d');
        $dateTo = $this->data['dateTo'] ?? Carbon::now()->format('Y-m-d');

        // Update URL without redirect for better UX
        $this->js("window.history.replaceState({}, '', '?' + new URLSearchParams({dateFrom: '{$dateFrom}', dateTo: '{$dateTo}'}).toString())");

        // Dispatch browser event for Alpine.js overlays
        $this->js("window.dispatchEvent(new CustomEvent('date-filters-updated'))");

        // Dispatch event to refresh widgets
        $this->dispatch('dateFiltersUpdated', [
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
    }

    protected function getFormActions(): array
    {
        return [];
    }

    public function getFilters(): array
    {
        return $this->data ?? [
            'dateFrom' => Carbon::now()->subDays(7)->format('Y-m-d'),
            'dateTo' => Carbon::now()->format('Y-m-d'),
        ];
    }

    private function getDateRangeFromSelection(string $selection): array
    {
        $now = Carbon::now();

        return match ($selection) {
            'last_7_days' => [
                $now->copy()->subDays(7)->format('Y-m-d'),
                $now->format('Y-m-d')
            ],
            'last_30_days' => [
                $now->copy()->subDays(30)->format('Y-m-d'),
                $now->format('Y-m-d')
            ],
            'month_to_date' => [
                $now->copy()->startOfMonth()->format('Y-m-d'),
                $now->format('Y-m-d')
            ],
            'week_to_date' => [
                $now->copy()->startOfWeek()->format('Y-m-d'),
                $now->format('Y-m-d')
            ],
            default => [
                $now->copy()->subDays(7)->format('Y-m-d'),
                $now->format('Y-m-d')
            ]
        };
    }
}
