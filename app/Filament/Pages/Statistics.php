<?php

namespace App\Filament\Pages;

use BackedEnum;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use UnitEnum;

class Statistics extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationLabel = 'Statistics';

    protected static ?string $title = 'Statistics';

    protected string $view = 'filament.pages.statistics';

    protected static string | null | BackedEnum $navigationIcon = Heroicon::OutlinedChartBar;

    protected static string|UnitEnum|null $navigationGroup = 'Analytics';

    public ?array $data = [];

    public function mount(): void
    {
        // Get dates from URL parameters or use defaults
        $dateFrom = request('dateFrom', Carbon::now()->subDays(7)->format('Y-m-d'));
        $dateTo = request('dateTo', Carbon::now()->format('Y-m-d'));

        $this->form->fill([
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                DatePicker::make('dateFrom')
                    ->label(__('From'))
                    ->required()
                    ->live()
                    ->native(false)
                    ->closeOnDateSelection()
                    ->afterStateUpdated(fn($state) => $this->updateDateFilters()),
                DatePicker::make('dateTo')
                    ->label(__('To'))
                    ->required()
                    ->native(false)
                    ->closeOnDateSelection()
                    ->maxDate(Carbon::now())
                    ->live()
                    ->afterStateUpdated(fn($state) => $this->updateDateFilters()),
            ])
            ->columns(2)
            ->statePath('data');
    }

    public function updateDateFilters(): void
    {
        // Update URL parameters and refresh widgets
        $dateFrom = $this->data['dateFrom'] ?? Carbon::now()->subDays(7)->format('Y-m-d');
        $dateTo = $this->data['dateTo'] ?? Carbon::now()->format('Y-m-d');

        // Update URL without redirect for better UX
        $this->js("window.history.replaceState({}, '', '?' + new URLSearchParams({dateFrom: '{$dateFrom}', dateTo: '{$dateTo}'}).toString())");

        // Dispatch browser event for Alpine.js overlays
        $this->js("window.dispatchEvent(new CustomEvent('date-filters-updated'))");

        // Dispatch event to refresh widgets
        $this->dispatch('dateFiltersUpdated', [
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
    }

    protected function getFormActions(): array
    {
        return [];
    }

    public function getFilters(): array
    {
        return $this->data ?? [
            'dateFrom' => Carbon::now()->subDays(7)->format('Y-m-d'),
            'dateTo' => Carbon::now()->format('Y-m-d'),
        ];
    }
}
