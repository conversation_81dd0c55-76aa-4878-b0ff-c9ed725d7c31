<?php

namespace App\Filament\Pages;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Actions;
use Filament\Actions\Action;
use App\Actions\PullZone\PurgeCache;
use App\Actions\PurgeUrl;
use App\Http\Middleware\VerifyBillableSubscribed;
use App\Services\Bunny;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

/**
 * @property \Filament\Schemas\Schema $form
 */
class Purge extends Page
{
    protected static string|array $routeMiddleware = [
        VerifyBillableSubscribed::class
    ];

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-bolt-slash';

    protected string $view = 'filament.pages.purge';

    protected static ?int $navigationSort = 40;

    protected static string|\UnitEnum|null $navigationGroup = 'Delivery';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([]);
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->statePath('data')
            ->components([
                Section::make('Purge URL List')
                    ->description('Purging an URL will remove the file from the CDN cache and re-download it from your origin server. Please enter the exact CDN URL of each individual file. You can also purge folders or wildcard files using * inside of the URL path.')
                    ->schema([
                        Textarea::make('urls')
                            ->rows(5)
                            ->placeholder('https://myhandle.skymage.net/v1/skymage.net/tomcat.jpg' . PHP_EOL . 'https://mytoken.skymage.net/v1/*')
                            ->required(),

                        Actions::make([
                            Action::make('purge_uls')
                                ->action($this->purgeUls(...))
                        ])
                    ]),

                Section::make('Purge everything')
                    ->description('This will purge all files from the CDN cache. This action is irreversible and will cause all files to be re-downloaded from your origin server.')
                    ->schema([
                        Actions::make([
                            Action::make('purge_all')
                                ->label('Purge them all!')
                                ->color('danger')
                                ->requiresConfirmation()
                                ->action(function () {
                                    PurgeCache::make()->handle(auth()->user()->pullZone);

                                    Notification::make()->title('Your purge request is queueing')->success()->send();
                                })
                        ])
                    ])
            ]);
    }

    public function purgeAll() {}

    public function purgeUls(array $data): void
    {
        $data = $this->form->getState();

        $urls = explode(PHP_EOL, $data['urls']);

        foreach ($urls as $url) {
            PurgeUrl::dispatch($url);
        }

        Notification::make()->title('Queuing purge urls')->success()->send();
    }
}
