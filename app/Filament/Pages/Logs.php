<?php

namespace App\Filament\Pages;

use App\Models\PullZoneStatus;
use Filament\Pages\Page;
use Filament\Support\Enums\Width;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Forms\Components\DatePicker;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Auth;
use UnitEnum;
use BackedEnum;
use Carbon\Carbon;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Enums\PaginationMode;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Number;

class Logs extends Page implements HasTable
{
    use InteractsWithTable;

    protected static string | BackedEnum | null $navigationIcon = 'heroicon-o-document-text';

    protected string $view = 'filament.pages.logs';

    protected static ?string $title = 'Logs';

    protected static string | UnitEnum | null $navigationGroup = 'Analytics';

    protected static ?int $navigationSort = 2;

    protected Width | string | null $maxContentWidth = 'full';

    public function mount(): void
    {

    }

    public function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->records(fn(array $filters, ?string $search, int $page, int $recordsPerPage) => $this->getLogsData($filters, $search, $page, $recordsPerPage))
            ->columns([
                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ((int) $state) {
                        200, 201, 202, 204 => 'success',
                        301, 302, 304 => 'warning',
                        400, 401, 403, 404 => 'danger',
                        500, 502, 503, 504 => 'danger',
                        default => 'gray',
                    })
                    ->tooltip(function (array $record) {
                        return 'Cache ' . $record['cache_status'] ?? '';
                    }),

                TextColumn::make('timestamp')
                    ->label('Timestamp')
                    ->dateTime('Y-m-d H:i:s'),

                TextColumn::make('country')
                    ->label('Country')
                    ->badge(),

                TextColumn::make('ip')
                    ->label('IP Address'),

                TextColumn::make('bytes')
                    ->label('Size')
                    ->numeric()
                    ->formatStateUsing(fn($state) => Number::fileSize($state)),


                TextColumn::make('url')
                    ->label('URL')
                    ->color('info')
                    ->url(fn(?string $state) => $state)
                    ->openUrlInNewTab(),

            ])
            ->paginationPageOptions([50])
            ->paginationMode(PaginationMode::Simple)
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        '200' => '200 - OK',
                        '300' => '301 - Moved Permanently',
                        '400' => '404 - Not Found',
                        '500' => '500 - Internal Server Error',
                    ])
                    ->placeholder('All statuses'),

                Filter::make('date')
                    ->label(null)
                    ->schema([
                        DatePicker::make('date')
                            ->placeholder('Select Date')
                            ->default(now()->format('Y-m-d'))
                            ->maxDate(now())
                            ->native(false)
                            ->closeOnDateSelection(),
                    ])
            ], layout: FiltersLayout::AboveContent)
            ->deferFilters(false);
    }

    protected function getLogsData(array $filters, ?string $search = null, int $page = 1, int $recordsPerPage = 50)
    {
        $logDate = $filters['date']['date'] ?? now()->format('Y-m-d');
        $status = $filters['status']['value'] ?? null;

        $logDate = Carbon::parse($logDate)->format('m-d-y');

        $user = Auth::user();
        $pullZone = $user->pullZone;

        if (!$pullZone || $pullZone->status !== PullZoneStatus::Active) {
            return new LengthAwarePaginator(
                collect(),
                0, // total
                $recordsPerPage,
                $page,
                ['path' => request()->url()]
            );
        }

        // Calculate start/end positions based on page and recordsPerPage
        $start = ($page - 1) * $recordsPerPage;
        $end = $start + $recordsPerPage;

        // Build query parameters for the API
        $queryParams = [];
        $queryParams['start'] = $start;
        $queryParams['end'] = $end;

        if ($status) {
            $queryParams['status'] = $status;
        }

        $url = "https://logging.bunnycdn.com/{$logDate}/{$pullZone->id_bunny}.log";

        $response = Http::withHeaders([
            'Accept' => 'application/json, text/plain, */*',
            'AccessKey' => config('services.bunny.api_key'),
        ])->get($url, $queryParams);

        if (!$response->successful() || !$response->body()) {
            return new LengthAwarePaginator(
                collect(),
                0, // total
                $recordsPerPage,
                $page,
                ['path' => request()->url()]
            );
        }

        // Parse pipe-delimited log data
        $lines = explode("\n", trim($response->body()));
        $logs = collect($lines)
            ->filter(fn($line) => !empty(trim($line)))
            ->map(function ($line) use ($pullZone) {
                $parts = explode('|', $line);

                // Ensure we have enough parts
                if (count($parts) < 12) {
                    return null;
                }

                return [
                    'cache_status' => $parts[0] ?? '',
                    'status' => $parts[1] ?? '',
                    'timestamp' => $parts[2] ? date('Y-m-d H:i:s', $parts[2] / 1000) : '',
                    'bytes' => $parts[3] ?? 0,
                    'zone_id' => $parts[4] ?? '',
                    'ip' => $parts[5] ?? '',
                    'referer' => $parts[6] === '-' ? '' : $parts[6],
                    'url' => $parts[7] ?? '',
                    'location' => $parts[8] ?? '',
                    'user_agent' => $parts[9] ?? '',
                    'request_id' => $parts[10] ?? '',
                    'country' => $parts[11] ?? '',
                ];
            })
            ->filter(); // Remove null entries

        // For cursor-based APIs, we need to estimate total count
        // Since we don't know the exact total, we'll use a large number
        // and let the pagination stop when no more records are returned
        $estimatedTotal = $start + $logs->count() + ($logs->count() >= $recordsPerPage ? $recordsPerPage : 0);

        return new LengthAwarePaginator(
            $logs,
            $estimatedTotal,
            $recordsPerPage,
            $page,
            [
                'path' => request()->url(),
                'pageName' => 'page',
            ]
        );
    }

    public function getSelectedTableRecordsQuery(bool $shouldFetchSelectedRecords = true, ?int $chunkSize = null): Builder
    {
        // Return a dummy query builder since we're using custom data
        return \App\Models\User::query()->whereRaw('1 = 0');
    }

    public function getPullZoneOptions(): array
    {
        $user = Auth::user();
        $pullZone = $user->pullZone;

        if (!$pullZone || $pullZone->status !== PullZoneStatus::Active) {
            return [];
        }

        return [$pullZone->id => $pullZone->name];
    }
}
