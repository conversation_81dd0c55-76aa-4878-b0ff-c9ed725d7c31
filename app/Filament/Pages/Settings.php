<?php

namespace App\Filament\Pages;

use App\Http\Middleware\VerifyBillableSubscribed;
use Filament\Pages\Page;

class Settings extends Page
{
    protected static string|array $routeMiddleware = [
        VerifyBillableSubscribed::class
    ];

    protected static ?string $navigationLabel = 'Images settings';

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-photo';

    protected string $view = 'filament.pages.settings';

    protected static ?int $navigationSort = 10;

    protected static string|\UnitEnum|null $navigationGroup = 'Delivery';
}
