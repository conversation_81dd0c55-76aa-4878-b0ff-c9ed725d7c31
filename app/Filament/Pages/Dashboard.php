<?php

namespace App\Filament\Pages;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Section;
use App\Filament\Widgets\DashboardStats;
use App\Http\Middleware\VerifyBillableSubscribed;
use App\Models\PullZone;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Pages\Page;
use Filament\Support\Enums\IconPosition;

class Dashboard extends Page implements HasInfolists
{
    use InteractsWithInfolists;

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-home';

    protected string $view = 'filament.pages.dashboard';

    protected static string|array $routeMiddleware = [
        VerifyBillableSubscribed::class
    ];

    public function getHeaderWidgets(): array
    {
        if (auth()->user()?->pullZone?->isProvisioning()) {
            return [];
        }

        return [
            DashboardStats::class
        ];
    }

    public function pullZoneInfolist(Schema $schema): Schema
    {
        return $schema
            ->record(auth()->user()->pullZone)
            ->components([
                Section::make('pull-zone')
                    ->heading(__('Your CDN'))
                    ->description('Your CDN url is used to pull images from your websites.')
                    ->columns(2)
                    ->schema([
                        TextEntry::make('url')
                            ->label('Base URL')
                            ->state(fn(PullZone $record) => 'https://' . $record->handle . '.skymage.net/v1')
                            ->copyable()
                            ->color('primary')
                            ->icon('heroicon-o-document-duplicate')
                            ->iconPosition(IconPosition::After),
                        TextEntry::make('status')
                            ->badge()
                            ->label('Status'),
                        TextEntry::make('handle')
                            ->label(__('Handle'))
                            ->copyable()
                            ->color('primary')
                            ->badge()
                            ->icon('heroicon-o-document-duplicate')
                            ->iconPosition(IconPosition::After),
                        TextEntry::make('bandwidth')
                            ->label(__('Bandwidth'))
                            ->suffix('/month')
                            ->state(fn(PullZone $record) => $record->monthlyBandwidthLimit() ? $record->monthlyBandwidthLimit() / 1_000_000_000 . ' GB' : 'Unlimited'),
                    ]),
            ]);
    }

    protected function getViewData(): array
    {
        return [
            'pullZone' => auth()->user()->pullZone
        ];
    }
}
