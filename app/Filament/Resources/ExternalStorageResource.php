<?php

namespace App\Filament\Resources;

use Filament\Facades\Filament;
use Filament\Schemas\Schema;
use Filament\Schemas\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Actions\Action;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use App\Filament\Resources\ExternalStorageResource\Pages\ListExternalStorages;
use App\Filament\Resources\ExternalStorageResource\Pages\CreateExternalStorage;
use App\Filament\Resources\ExternalStorageResource\Pages\EditExternalStorage;
use App\Enums\ExternalStorageDriver;
use App\Enums\ExternalStorageStatus;
use App\Filament\Resources\ExternalStorageResource\Pages;
use App\Http\Middleware\VerifyBillableSubscribed;
use App\Models\ExternalStorage;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class ExternalStorageResource extends Resource
{
    protected static string|array $routeMiddleware = [
        VerifyBillableSubscribed::class
    ];

    protected static ?string $model = ExternalStorage::class;

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-server-stack';

    protected static ?int $navigationSort = 30;

    protected static string|\UnitEnum|null $navigationGroup = 'Delivery';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('user_id', Filament::auth()->user()?->id);
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Section::make()
                    ->schema([
                        TextInput::make('name')
                            ->label(__('Storage Name'))
                            ->required()
                            ->maxLength(255)
                            ->placeholder(__('My S3 Storage')),

                        TextInput::make('handle')
                            ->label(__('Handle'))
                            ->helperText(__('Unique identifier for this storage'))
                            ->required()
                            ->maxLength(64)
                            ->alphaDash()
                            ->placeholder(__('my-storage-handle'))
                            ->unique(
                                table: 'external_storages',
                                column: 'handle',
                                ignoreRecord: true,
                                modifyRuleUsing: function ($rule, $get) {
                                    return $rule->where('user_id', Filament::auth()->user()?->id);
                                }
                            ),

                        Select::make('driver')
                            ->label(__('Storage Driver'))
                            ->required()
                            ->columns(3)
                            ->options(ExternalStorageDriver::class)
                            ->live()
                            ->afterStateUpdated(fn(Set $set) => $set('configuration', [])),

                        Select::make('status')
                            ->label(__('Status'))
                            ->options(ExternalStorageStatus::class)
                            ->hiddenOn('create')
                            ->helperText(__('Status will be updated after testing the connection, you can disable it temporarily.')),
                    ]),

                Section::make(__('Connection Settings'))
                    ->schema([
                        // Bunny Storage Configuration
                        Group::make([
                            TextInput::make('configuration.storage_zone')
                                ->label(__('Storage Zone'))
                                ->required()
                                ->maxLength(255),

                            TextInput::make('configuration.api_key')
                                ->label(__('API Key'))
                                ->password()
                                ->revealable()
                                ->helperText(__('Use readonly password for security'))
                                ->required()
                                ->maxLength(255),

                            TextInput::make('configuration.region')
                                ->label(__('Region'))
                                ->default('de')
                                ->maxLength(10),
                        ])
                            ->visible(fn(Get $get) => $get('driver') === ExternalStorageDriver::Bunny->value),

                        // S3 Configuration
                        Group::make([
                            Placeholder::make('s3_help')
                                ->label(__('AWS S3 Setup Guide'))
                                ->content(__('1. Create an S3 bucket in your AWS console<br>2. Note the exact region where you created the bucket<br>3. Create IAM credentials with S3 access permissions<br>4. Test the connection after entering your details'))
                                ->columnSpanFull(),
                            TextInput::make('configuration.access_key_id')
                                ->label(__('Access Key ID'))
                                ->required()
                                ->maxLength(255)
                                ->helperText(__('AWS IAM Access Key ID (starts with AKIA...)')),

                            TextInput::make('configuration.secret_access_key')
                                ->label(__('Secret Access Key'))
                                ->password()
                                ->revealable()
                                ->required()
                                ->maxLength(255)
                                ->helperText(__('AWS IAM Secret Access Key (keep this secure!)')),

                            TextInput::make('configuration.bucket')
                                ->label(__('Bucket Name'))
                                ->required()
                                ->maxLength(255)
                                ->helperText(__('Enter the exact name of your S3 bucket (case-sensitive)'))
                                ->placeholder(__('my-bucket-name')),

                            Select::make('configuration.region')
                                ->label(__('Region'))
                                ->required()
                                ->default('us-east-1')
                                ->helperText(__('Select the AWS region where your S3 bucket is located. If you get a 400/301 error, check your bucket\'s actual region in the AWS console.')),

                            TextInput::make('configuration.endpoint')
                                ->label(__('Custom Endpoint'))
                                ->url()
                                ->maxLength(255)
                                ->helperText(__('Leave empty for AWS S3')),
                        ])
                            ->visible(fn(Get $get) => $get('driver') === ExternalStorageDriver::S3->value),

                        // FTP Configuration
                        Group::make([
                            TextInput::make('configuration.host')
                                ->label(__('Host'))
                                ->required()
                                ->maxLength(255),

                            TextInput::make('configuration.port')
                                ->label(__('Port'))
                                ->numeric()
                                ->default(21)
                                ->minValue(1)
                                ->maxValue(65535),

                            TextInput::make('configuration.username')
                                ->label(__('Username'))
                                ->required()
                                ->maxLength(255),

                            TextInput::make('configuration.password')
                                ->label(__('Password'))
                                ->password()
                                ->revealable()
                                ->required()
                                ->maxLength(255),

                            TextInput::make('configuration.root_path')
                                ->label(__('Root Path'))
                                ->default('/')
                                ->maxLength(255),

                            Toggle::make('configuration.ssl')
                                ->label(__('Use SSL/TLS'))
                                ->default(false),

                            Toggle::make('configuration.passive')
                                ->label(__('Passive Mode'))
                                ->default(true),
                        ])
                            ->visible(fn(Get $get) => $get('driver') === ExternalStorageDriver::FTP->value),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->minimal()
            ->columns([
                TextColumn::make('name')
                    ->label(__('Name')),

                TextColumn::make('handle')
                    ->label(__('Handle'))
                    ->copyable()
                    ->badge()
                    ->color('primary'),

                TextColumn::make('driver')
                    ->label(__('Driver'))
                    ->badge(),

                TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge(),

                TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->dateTime(),
            ])
            ->defaultSort('created_at', 'desc')
            ->actions([
                Action::make('test_connection')
                    ->label(__('Test Connection'))
                    ->icon('heroicon-o-arrow-path')
                    ->tooltip(__('Test the connection to this storage'))
                    ->color('info')
                    ->action(function (ExternalStorage $record) {
                        $result = $record->testConnection();

                        if ($result['success']) {
                            Notification::make()
                                ->title(__('Connection successful'))
                                ->body($result['message'])
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title(__('Connection failed'))
                                ->body($result['message'])
                                ->danger()
                                ->send();
                        }
                    }),

                EditAction::make(),

                DeleteAction::make(),
            ])
            ->emptyStateHeading(__('No external storage configured'))
            ->emptyStateDescription(__('Connect your first external storage to get started.'))
            ->emptyStateIcon('heroicon-o-cloud-arrow-up');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListExternalStorages::route('/'),
            'create' => CreateExternalStorage::route('/create'),
            'edit' => EditExternalStorage::route('/{record}/edit'),
        ];
    }
}
