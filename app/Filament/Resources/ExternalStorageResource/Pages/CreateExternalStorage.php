<?php

namespace App\Filament\Resources\ExternalStorageResource\Pages;

use Filament\Facades\Filament;
use App\Filament\Resources\ExternalStorageResource;
use Filament\Resources\Pages\CreateRecord;

class CreateExternalStorage extends CreateRecord
{
    protected static string $resource = ExternalStorageResource::class;

    protected static ?string $title = 'Connect External Storage';

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['user_id'] = Filament::auth()->user()?->id;

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
