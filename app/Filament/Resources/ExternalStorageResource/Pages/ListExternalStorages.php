<?php

namespace App\Filament\Resources\ExternalStorageResource\Pages;

use Filament\Actions\CreateAction;
use App\Filament\Resources\ExternalStorageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;

class ListExternalStorages extends ListRecords
{
    protected static string $resource = ExternalStorageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->label(__('Add External Storage'))
                ->icon('heroicon-o-plus'),
        ];
    }
}
