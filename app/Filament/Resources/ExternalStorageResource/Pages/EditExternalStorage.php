<?php

namespace App\Filament\Resources\ExternalStorageResource\Pages;

use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Actions\DeleteAction;
use App\Filament\Resources\ExternalStorageResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditExternalStorage extends EditRecord
{
    protected static string $resource = ExternalStorageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('test_connection')
                ->label(__('Test Connection'))
                ->icon('heroicon-o-wifi')
                ->color('info')
                ->action(function () {
                    $result = $this->record->testConnection();

                    if ($result) {
                        Notification::make()
                            ->title(__('Connection successful'))
                            ->success()
                            ->send();
                    } else {
                        Notification::make()
                            ->title(__('Connection failed'))
                            ->danger()
                            ->send();
                    }
                }),
            DeleteAction::make(),
        ];
    }
}
