<?php

namespace App\Filament\Widgets;

use App\Filament\Widgets\Concerns\HasDateFilters;
use App\Services\Bunny;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class RequestsChart extends ChartWidget
{
    use HasDateFilters;

    protected ?string $heading = 'Requests Served';

    protected static ?int $sort = 3;

    protected ?string $maxHeight = '350px';


    protected function getData(): array
    {
        $pullZone = Auth::user()?->pullZone;

        if (!$pullZone) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        // Get filters using trait methods
        $dateFrom = $this->getDateFromFilter();
        $dateTo = $this->getDateToFilter();

        $stats = Cache::remember(
            $this->getCacheKey('requests-chart', $pullZone->id_bunny),
            now()->addMinutes(30),
            function () use ($pullZone, $dateFrom, $dateTo) {
                try {
                    $bunny = new Bunny(config('services.bunny.api_key'));

                    return $bunny->statistics([
                        'pullZone' => $pullZone->id_bunny,
                        'dateFrom' => $dateFrom,
                        'dateTo' => $dateTo,
                        'hourly' => true,
                    ]);
                } catch (\Exception $e) {
                    // Return empty data when API call fails
                    return [
                        'RequestsServedChart' => [],
                        'PullRequestsPulledChart' => [],
                    ];
                }
            }
        );

        if (!$stats || !is_array($stats)) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        $labels = [];
        $totalRequests = [];
        $originRequests = [];

        // Extract chart data from the API response
        $requestsChart = $stats['RequestsServedChart'] ?? [];
        $pullRequestsChart = $stats['PullRequestsPulledChart'] ?? [];

        foreach ($requestsChart as $timestamp => $requests) {
            $labels[] = Carbon::parse($timestamp)->format('M j');
            $totalRequests[] = $requests;
            $originRequests[] = $pullRequestsChart[$timestamp] ?? 0;
        }

        return [
            'datasets' => [
                [
                    'label' => __('Total Requests'),
                    'data' => $totalRequests,
                    'backgroundColor' => 'rgba(59, 130, 246, 0.8)',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'borderWidth' => 1,
                    'borderRadius' => 6,
                    'borderSkipped' => false,
                ],
                [
                    'label' => __('Origin Requests'),
                    'data' => $originRequests,
                    'backgroundColor' => 'rgba(34, 197, 94, 0.8)',
                    'borderColor' => 'rgb(34, 197, 94)',
                    'borderWidth' => 1,
                    'borderRadius' => 6,
                    'borderSkipped' => false,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                ],
                'x' => [
                    'title' => [
                        'display' => true,
                        'text' => __('Time'),
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                ],
            ],
        ];
    }
}
