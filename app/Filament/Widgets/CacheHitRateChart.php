<?php

namespace App\Filament\Widgets;

use App\Services\Bunny;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class CacheHitRateChart extends ChartWidget
{
    protected ?string $heading = 'Cache Hit Rate';

    protected static ?int $sort = 4;

    protected $listeners = ['dateFiltersUpdated' => 'updateDateFilters'];

    public ?string $dateFrom = null;
    public ?string $dateTo = null;

    public function updateDateFilters($data): void
    {
        $this->dateFrom = $data['dateFrom'] ?? null;
        $this->dateTo = $data['dateTo'] ?? null;
        $this->dispatch('$refresh');
    }

    public function mount(): void
    {
        // Initialize with default or URL parameters
        $this->dateFrom = request('dateFrom', Carbon::now()->subDays(7)->format('Y-m-d'));
        $this->dateTo = request('dateTo', Carbon::now()->format('Y-m-d'));
    }

    protected function getData(): array
    {
        $pullZone = Auth::user()?->pullZone;

        if (!$pullZone) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        // Get filters from widget properties or request parameters
        $dateFrom = $this->dateFrom ?? request('dateFrom', Carbon::now()->subDays(7)->format('Y-m-d'));
        $dateTo = $this->dateTo ?? request('dateTo', Carbon::now()->format('Y-m-d'));

        $stats = Cache::remember(
            "cache-hit-chart-{$pullZone->id_bunny}-{$dateFrom}-{$dateTo}",
            now()->addMinutes(30),
            function () use ($pullZone, $dateFrom, $dateTo) {
                try {
                    $bunny = new Bunny(config('services.bunny.api_key'));

                    return $bunny->statistics([
                        'pullZone' => $pullZone->id_bunny,
                        'dateFrom' => $dateFrom,
                        'dateTo' => $dateTo,
                        'hourly' => true,
                    ]);
                } catch (\Exception $e) {
                    // Return empty data when API call fails
                    return [
                        'CacheHitRateChart' => [],
                    ];
                }
            }
        );

        if (!$stats || !is_array($stats)) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        $labels = [];
        $cacheHitRate = [];

        // Extract chart data from the API response
        $cacheHitChart = $stats['CacheHitRateChart'] ?? [];

        foreach ($cacheHitChart as $timestamp => $hitRate) {
            $labels[] = Carbon::parse($timestamp)->format('M j');
            $cacheHitRate[] = round($hitRate, 2);
        }

        return [
            'datasets' => [
                [
                    'label' => __('Cache Hit Rate (%)'),
                    'data' => $cacheHitRate,
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                    'borderColor' => 'rgb(245, 158, 11)',
                    'borderWidth' => 2,
                    'fill' => true,
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'max' => 100,
                ],
                'x' => [
                    'title' => [
                        'display' => true,
                        'text' => __('Time'),
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                ],
            ],
        ];
    }
}
