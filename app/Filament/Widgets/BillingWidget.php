<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class BillingWidget extends StatsOverviewWidget
{
    public function getStats(): array
    {
        $user = auth()->user();
        $subscription = $user->subscription();
        $plan = $user->plan();

        return [
            Stat::make('You current plan', $plan?->name ?? '--')
                ->description($plan?->interval),
            Stat::make('Next payment date', $subscription->renews_at->toDateString()),
        ];
    }
}
