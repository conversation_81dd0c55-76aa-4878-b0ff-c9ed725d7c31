<?php

namespace App\Filament\Widgets;

use App\Filament\Widgets\Concerns\HasDateFilters;
use App\Services\Bunny;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class StatisticsOverview extends BaseWidget
{
    use HasDateFilters;

    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        $pullZone = Auth::user()?->pullZone;

        if (!$pullZone) {
            return [];
        }

        // Get filters using trait methods
        $dateFrom = $this->getDateFromFilter();
        $dateTo = $this->getDateToFilter();

        $stats = Cache::remember(
            $this->getCacheKey('stats-overview', $pullZone->id_bunny),
            now()->addMinutes(30),
            function () use ($pullZone, $dateFrom, $dateTo) {
                try {
                    $bunny = app(Bunny::class);

                    return $bunny->statistics([
                        'pullZone' => $pullZone->id_bunny,
                        'dateFrom' => $dateFrom,
                        'dateTo' => $dateTo,
                    ]);
                } catch (\Exception $e) {
                    // Return empty data when API call fails
                    return [
                        'TotalRequestsServed' => 0,
                        'TotalBandwidthUsed' => 0,
                        'CacheHitRate' => 0,
                        'AverageOriginResponseTime' => 0,
                    ];
                }
            }
        );

        if (!$stats || !is_array($stats)) {
            return [];
        }

        // Extract totals from the API response
        $totalRequests = $stats['TotalRequestsServed'] ?? 0;
        $totalBandwidth = $stats['TotalBandwidthUsed'] ?? 0;
        $cacheHitRatio = $stats['CacheHitRate'] ?? 0;
        $averageOriginResponseTime = $stats['AverageOriginResponseTime'] ?? 0;

        return [
            Stat::make(__('Total Requests'), number_format($totalRequests ?: 0))
                ->color('primary'),

            Stat::make(__('Bandwidth Used'), $this->formatBytes($totalBandwidth ?: 0))
                ->color('success'),

            Stat::make(__('Cache Hit Rate'), number_format($cacheHitRatio ?: 0, 2) . '%')
                ->color('warning'),
        ];
    }

    private function formatBytes(float|int|null $bytes): string
    {
        if ($bytes === null || $bytes <= 0) {
            return '0 B';
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}
