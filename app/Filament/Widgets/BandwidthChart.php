<?php

namespace App\Filament\Widgets;

use App\Filament\Widgets\Concerns\HasDateFilters;
use App\Services\Bunny;
use Carbon\Carbon;
use Filament\Support\RawJs;
use Filament\Widgets\ChartWidget;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class BandwidthChart extends ChartWidget
{
    use HasDateFilters;

    protected ?string $heading = 'Bandwidth Usage';

    protected static ?int $sort = 2;

    protected ?string $maxHeight = '350px';


    protected function getData(): array
    {
        $pullZone = Auth::user()?->pullZone;

        if (!$pullZone) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        // Get filters using trait methods
        $dateFrom = $this->getDateFromFilter();
        $dateTo = $this->getDateToFilter();

        $stats = Cache::remember(
            $this->getCacheKey('bandwidth-chart', $pullZone->id_bunny),
            now()->addMinutes(30),
            function () use ($pullZone, $dateFrom, $dateTo) {
                try {
                    $bunny = new Bunny(config('services.bunny.api_key'));

                    return $bunny->statistics([
                        'pullZone' => $pullZone->id_bunny,
                        'dateFrom' => $dateFrom,
                        'dateTo' => $dateTo,
                        'hourly' => true,
                    ]);
                } catch (\Exception $e) {
                    // Return empty data when API call fails
                    return [
                        'BandwidthUsedChart' => [],
                        'BandwidthCachedChart' => [],
                    ];
                }
            }
        );

        if (!$stats || !is_array($stats)) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        $labels = [];
        $bandwidthData = [];
        $uncachedData = [];

        // Extract chart data from the API response
        $bandwidthChart = $stats['BandwidthUsedChart'] ?? [];
        $cachedChart = $stats['BandwidthCachedChart'] ?? [];

        foreach ($bandwidthChart as $timestamp => $bandwidth) {
            $labels[] = Carbon::parse($timestamp)->format('M j');
            $totalBandwidth = round($bandwidth / 1024 / 1024 / 1024, 2); // Convert to GB
            $cachedBandwidth = round(($cachedChart[$timestamp] ?? 0) / 1024 / 1024 / 1024, 2); // Convert to GB
            $uncachedBandwidth = round($totalBandwidth - $cachedBandwidth, 2); // Calculate uncached bandwidth

            $bandwidthData[] = $totalBandwidth;
            $uncachedData[] = max(0, $uncachedBandwidth); // Ensure non-negative values
        }

        return [
            'datasets' => [
                [
                    'label' => __('Total Bandwidth'),
                    'data' => $bandwidthData,
                    'backgroundColor' => 'rgba(37, 99, 235, 0.8)',
                    'borderColor' => 'rgb(37, 99, 235)',
                    'borderWidth' => 1,
                    'borderRadius' => 6,
                    'borderSkipped' => false,
                ],
                [
                    'label' => __('Uncached Bandwidth'),
                    'data' => $uncachedData,
                    'backgroundColor' => 'rgba(34, 197, 94, 0.8)',
                    'borderColor' => 'rgb(34, 197, 94)',
                    'borderWidth' => 1,
                    'borderRadius' => 6,
                    'borderSkipped' => false,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                ],
                'x' => [
                    'title' => [
                        'display' => true,
                        'text' => __('Time'),
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                ],
            ],
        ];
    }
}
