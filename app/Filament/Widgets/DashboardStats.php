<?php

namespace App\Filament\Widgets;

use App\Actions\PullZone\GetPullZoneStatistics;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class DashboardStats extends BaseWidget
{
    protected ?string $pollingInterval = null;

    protected static bool $isLazy = true;

    protected function getStats(): array
    {
        $pullZone = auth()->user()->pullZone;

        if (!$pullZone) {
            return [];
        }

        // from start of month to now
        $stats = Cache::remember(
            'pull-zone-stats-' . $pullZone->id_bunny . '-' . now()->format('Y-m'),
            now()->addHour(),
            fn() => rescue(
                fn() => GetPullZoneStatistics::make()->handle($pullZone, [
                    'dateFrom' => now()->startOfMonth()->toDateTimeString(),
                    'dateTo'   => now()->toDateTimeString(),
                    'hourly'   => true,
                ]),
                function ($exception) {
                    report($exception);

                    return [];
                }
            ),
        );

        // Check if stats is empty or missing required keys
        if (empty($stats) || !$this->hasRequiredStatsKeys($stats)) {
            return $this->getDefaultStats();
        }

        return [
            Stat::make(
                __('Requests served'),
                number_format($stats['TotalRequestsServed'])
            )
                ->chart($stats['RequestsServedChart'] ?? [])
                ->chartColor('success'),
            Stat::make(
                __('Bandwidth used'),
                number_format($stats['TotalBandwidthUsed'] / 1000000000, 2) . ' GB'
            )
                ->chart($stats['BandwidthUsedChart'] ?? [])
                ->chartColor('warning'),
            Stat::make(
                __('Cache hit rate'),
                number_format($stats['CacheHitRate'], 2) . ' %'
            )->chart($stats['CacheHitRateChart'] ?? [])
                ->chartColor('gray'),
        ];
    }

    /**
     * Check if the stats array has all required keys
     */
    private function hasRequiredStatsKeys(array $stats): bool
    {
        $requiredKeys = [
            'TotalRequestsServed',
            'TotalBandwidthUsed',
            'CacheHitRate'
        ];

        foreach ($requiredKeys as $key) {
            if (!array_key_exists($key, $stats)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Return default stats when API is unavailable
     */
    private function getDefaultStats(): array
    {
        return [
            Stat::make(
                __('Requests served'),
                __('--')
            )
                ->description(__('Statistics temporarily unavailable'))
                ->chartColor('gray'),
            Stat::make(
                __('Bandwidth used'),
                __('--')
            )
                ->description(__('Statistics temporarily unavailable'))
                ->chartColor('gray'),
            Stat::make(
                __('Cache hit rate'),
                __('--')
            )
                ->description(__('Statistics temporarily unavailable'))
                ->chartColor('gray'),
        ];
    }
}
