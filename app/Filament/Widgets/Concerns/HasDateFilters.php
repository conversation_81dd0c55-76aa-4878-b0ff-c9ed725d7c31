<?php

namespace App\Filament\Widgets\Concerns;

use Carbon\Carbon;
use Livewire\Attributes\On;

trait HasDateFilters
{
    public ?string $dateFrom = null;
    public ?string $dateTo = null;

    #[On('dateFiltersUpdated')]
    public function updateDateFilters($data): void
    {
        $this->dateFrom = $data['dateFrom'] ?? null;
        $this->dateTo = $data['dateTo'] ?? null;
        $this->dispatch('$refresh');
    }

    public function mount(): void
    {
        // Initialize with default or URL parameters
        $this->dateFrom = request('dateFrom', Carbon::now()->subDays(7)->format('Y-m-d'));
        $this->dateTo = request('dateTo', Carbon::now()->format('Y-m-d'));
    }

    protected function getDateFromFilter(): string
    {
        return $this->dateFrom ?? request('dateFrom', Carbon::now()->subDays(7)->format('Y-m-d'));
    }

    protected function getDateToFilter(): string
    {
        return $this->dateTo ?? request('dateTo', Carbon::now()->format('Y-m-d'));
    }

    protected function getCacheKey(string $prefix, ?string $pullZoneId = null): string
    {
        $dateFrom = $this->getDateFromFilter();
        $dateTo = $this->getDateToFilter();

        if ($pullZoneId) {
            return "{$prefix}-{$pullZoneId}-{$dateFrom}-{$dateTo}";
        }

        return "{$prefix}-{$dateFrom}-{$dateTo}";
    }
}
