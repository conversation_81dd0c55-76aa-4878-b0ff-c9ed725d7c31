<?php

namespace App\Filament\Widgets\Concerns;

use Carbon\Carbon;
use Livewire\Attributes\On;

trait HasDateFilters
{
    public ?string $dateFrom = null;
    public ?string $dateTo = null;

    #[On('dateFiltersUpdated')]
    public function updateDateFilters($data): void
    {
        $this->dateFrom = $data['dateFrom'] ?? null;
        $this->dateTo = $data['dateTo'] ?? null;
        $this->dispatch('$refresh');
    }

    public function mount(): void
    {
        // Initialize with default or URL parameters
        // Handle both old dateFrom/dateTo and new dateRange parameters for backward compatibility
        if (request()->has('dateRange')) {
            [$dateFrom, $dateTo] = $this->getDateRangeFromSelection(request('dateRange', 'last_7_days'));
            $this->dateFrom = $dateFrom;
            $this->dateTo = $dateTo;
        } else {
            $this->dateFrom = request('dateFrom', Carbon::now()->subDays(7)->format('Y-m-d'));
            $this->dateTo = request('dateTo', Carbon::now()->format('Y-m-d'));
        }
    }

    protected function getDateFromFilter(): string
    {
        if ($this->dateFrom) {
            return $this->dateFrom;
        }

        // Handle both old and new URL parameter formats
        if (request()->has('dateRange')) {
            [$dateFrom, ] = $this->getDateRangeFromSelection(request('dateRange', 'last_7_days'));
            return $dateFrom;
        }

        return request('dateFrom', Carbon::now()->subDays(7)->format('Y-m-d'));
    }

    protected function getDateToFilter(): string
    {
        if ($this->dateTo) {
            return $this->dateTo;
        }

        // Handle both old and new URL parameter formats
        if (request()->has('dateRange')) {
            [, $dateTo] = $this->getDateRangeFromSelection(request('dateRange', 'last_7_days'));
            return $dateTo;
        }

        return request('dateTo', Carbon::now()->format('Y-m-d'));
    }

    protected function getCacheKey(string $prefix, ?string $pullZoneId = null): string
    {
        $dateFrom = $this->getDateFromFilter();
        $dateTo = $this->getDateToFilter();

        if ($pullZoneId) {
            return "{$prefix}-{$pullZoneId}-{$dateFrom}-{$dateTo}";
        }

        return "{$prefix}-{$dateFrom}-{$dateTo}";
    }

    private function getDateRangeFromSelection(string $selection): array
    {
        $now = Carbon::now();

        return match ($selection) {
            'last_7_days' => [
                $now->copy()->subDays(7)->format('Y-m-d'),
                $now->format('Y-m-d')
            ],
            'last_30_days' => [
                $now->copy()->subDays(30)->format('Y-m-d'),
                $now->format('Y-m-d')
            ],
            'month_to_date' => [
                $now->copy()->startOfMonth()->format('Y-m-d'),
                $now->format('Y-m-d')
            ],
            'week_to_date' => [
                $now->copy()->startOfWeek()->format('Y-m-d'),
                $now->format('Y-m-d')
            ],
            default => [
                $now->copy()->subDays(7)->format('Y-m-d'),
                $now->format('Y-m-d')
            ]
        };
    }
}
