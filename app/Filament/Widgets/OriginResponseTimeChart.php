<?php

namespace App\Filament\Widgets;

use App\Services\Bunny;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class OriginResponseTimeChart extends ChartWidget
{
    protected ?string $heading = 'Origin Response Time';

    protected static ?int $sort = 5;

    protected $listeners = ['dateFiltersUpdated' => 'updateDateFilters'];

    public ?string $dateFrom = null;
    public ?string $dateTo = null;

    public function updateDateFilters($data): void
    {
        $this->dateFrom = $data['dateFrom'] ?? null;
        $this->dateTo = $data['dateTo'] ?? null;
        $this->dispatch('$refresh');
    }

    public function mount(): void
    {
        // Initialize with default or URL parameters
        $this->dateFrom = request('dateFrom', Carbon::now()->subDays(7)->format('Y-m-d'));
        $this->dateTo = request('dateTo', Carbon::now()->format('Y-m-d'));
    }

    protected function getData(): array
    {
        $pullZone = Auth::user()?->pullZone;

        if (!$pullZone) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        // Get filters from widget properties or request parameters
        $dateFrom = $this->dateFrom ?? request('dateFrom', Carbon::now()->subDays(7)->format('Y-m-d'));
        $dateTo = $this->dateTo ?? request('dateTo', Carbon::now()->format('Y-m-d'));

        $stats = Cache::remember(
            "response-time-chart-{$pullZone->id_bunny}-{$dateFrom}-{$dateTo}",
            now()->addMinutes(30),
            function () use ($pullZone, $dateFrom, $dateTo) {
                try {
                    $bunny = new Bunny(config('services.bunny.api_key'));

                    return $bunny->statistics([
                        'pullZone' => $pullZone->id_bunny,
                        'dateFrom' => $dateFrom,
                        'dateTo' => $dateTo,
                        'hourly' => true,
                    ]);
                } catch (\Exception $e) {
                    // Return empty data when API call fails
                    return [
                        'OriginResponseTimeChart' => [],
                    ];
                }
            }
        );

        // Reset loading state after getting data

        if (!$stats || !is_array($stats)) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        $labels = [];
        $responseTime = [];

        // Extract chart data from the API response
        $responseTimeChart = $stats['OriginResponseTimeChart'] ?? [];

        foreach ($responseTimeChart as $timestamp => $time) {
            $labels[] = Carbon::parse($timestamp)->format('M j');
            $responseTime[] = round($time, 2);
        }

        return [
            'datasets' => [
                [
                    'label' => __('Response Time (ms)'),
                    'data' => $responseTime,
                    'backgroundColor' => 'rgba(99, 102, 241, 0.1)',
                    'borderColor' => 'rgb(99, 102, 241)',
                    'borderWidth' => 2,
                    'fill' => true,
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                ],
                'x' => [
                    'title' => [
                        'display' => true,
                        'text' => __('Time'),
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                ],
            ],
        ];
    }
}
