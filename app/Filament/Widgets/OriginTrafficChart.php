<?php

namespace App\Filament\Widgets;

use App\Filament\Widgets\Concerns\HasDateFilters;
use App\Services\Bunny;
use Carbon\Carbon;
use Filament\Support\RawJs;
use Filament\Widgets\ChartWidget;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class OriginTrafficChart extends ChartWidget
{
    use HasDateFilters;

    protected ?string $heading = 'Origin Traffic';

    protected static ?int $sort = 4;

    protected ?string $maxHeight = '350px';

    protected function getData(): array
    {
        $pullZone = Auth::user()?->pullZone;

        if (!$pullZone) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        // Get filters using trait methods
        $dateFrom = $this->getDateFromFilter();
        $dateTo = $this->getDateToFilter();

        $stats = Cache::remember(
            $this->getCacheKey('origin-traffic-chart', $pullZone->id_bunny),
            now()->addMinutes(30),
            function () use ($pullZone, $dateFrom, $dateTo) {
                try {
                    $bunny = new Bunny(config('services.bunny.api_key'));

                    return $bunny->statistics([
                        'pullZone' => $pullZone->id_bunny,
                        'dateFrom' => $dateFrom,
                        'dateTo' => $dateTo,
                        'hourly' => true,
                    ]);
                } catch (\Exception $e) {
                    // Return empty data when API call fails
                    return [
                        'OriginTrafficChart' => [],
                    ];
                }
            }
        );

        if (!$stats || !is_array($stats)) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        $labels = [];
        $originTraffic = [];

        // Extract chart data from the API response
        $originTrafficChart = $stats['OriginTrafficChart'] ?? [];

        foreach ($originTrafficChart as $timestamp => $traffic) {
            $labels[] = Carbon::parse($timestamp)->format('M j');
            // Convert bytes to GB
            $originTraffic[] = round($traffic / (1024 * 1024 * 1024), 3);
        }

        return [
            'datasets' => [
                [
                    'label' => __('Origin Traffic'),
                    'data' => $originTraffic,
                    'backgroundColor' => 'rgba(168, 85, 247, 0.1)',
                    'borderColor' => 'rgb(168, 85, 247)',
                    'borderWidth' => 2,
                    'fill' => true,
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): RawJs
    {
        return RawJs::make(<<<JS
            {
                responsive: true,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 0.5,
                            callback: (value) => value + 'GB',
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Time'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += context.parsed.y + 'GB';
                                return label;
                            }
                        }
                    }
                }
            }
        JS);
    }
}
