<?php

namespace App\Filament\Backoffice\Resources;

use Filament\Schemas\Schema;
use App\Filament\Backoffice\Resources\UserResource\Pages\CreateUser;
use Filament\Actions\EditAction;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use App\Filament\Backoffice\Resources\UserResource\Pages\ListUsers;
use App\Filament\Backoffice\Resources\UserResource\Pages\EditUser;
use App\Actions\CreateFreeTrial;
use App\Actions\ProvisionCDN;
use App\Filament\Backoffice\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-users';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label(__('Name'))
                    ->required()
                    ->maxLength(255),

                TextInput::make('email')
                    ->label(__('Email'))
                    ->email()
                    ->required()
                    ->maxLength(255)
                    ->unique(User::class, 'email', fn ($record) => $record),

                TextInput::make('password')
                    ->label(__('Password'))
                    ->password()
                    ->dehydrated(fn ($state) => filled($state))
                    ->required(fn ($livewire) => $livewire instanceof CreateUser)
                    ->minLength(8)
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('name'),
                TextColumn::make('email')->searchable(),
                TextColumn::make('pullZone.handle')
                    ->label('Pull Zone handle')
                    ->copyable()
                    ->searchable(),
                IconColumn::make('on_trial')
                    ->state(fn(User $record) => $record->onTrial())
                    ->boolean(),
                IconColumn::make('subscribed')
                    ->state(fn(User $record) => $record->subscribed())
                    ->boolean(),
                TextColumn::make('subscription_plan')
                    ->state(fn(User $record) => $record->subscribed() ? $record->plan()?->name : '--'),
                TextColumn::make('trial_ends_at')
                    ->state(fn(User $record) => $record->onTrial() ? $record->trialEndsAt() : null),
                TextColumn::make('created_at')->dateTime()->toggleable(),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
                Action::make('start-free-trial')
                    ->icon('heroicon-o-clock')
                    ->label('Start free trial')
                    ->tooltip('Start free trial')
                    ->disabled(fn(User $record) => $record->subscribed() || $record->onTrial())
                    ->action(function (User $record) {
                        CreateFreeTrial::make()->handle($record);

                        Notification::make()->title("Started free trial...")->success()->send();
                    }),
                Action::make('provision-pull-zone')
                    ->label('Provision pull zone')
                    ->tooltip('Provision pull zone')
                    ->icon('heroicon-o-cloud-arrow-up')
                    ->disabled(fn(User $record) => $record->pullZone)
                    ->schema([
                        TextInput::make('handle')
                            ->helperText('This function is for custom pull zone handle')
                    ])
                    ->action(function (User $record, array $data) {
                        ProvisionCDN::make()->handle($record, $data['handle']);

                        Notification::make()->title("Provisioning pull zone...")->info()->send();
                    })
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => ListUsers::route('/'),
            'create' => CreateUser::route('/create'),
            'edit'   => EditUser::route('/{record}/edit'),
        ];
    }
}
