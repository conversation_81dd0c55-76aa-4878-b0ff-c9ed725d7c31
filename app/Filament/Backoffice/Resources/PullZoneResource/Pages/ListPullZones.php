<?php

namespace App\Filament\Backoffice\Resources\PullZoneResource\Pages;

use Filament\Actions\CreateAction;
use App\Filament\Backoffice\Resources\PullZoneResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPullZones extends ListRecords
{
    protected static string $resource = PullZoneResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
