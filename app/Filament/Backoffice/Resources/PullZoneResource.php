<?php

namespace App\Filament\Backoffice\Resources;

use Filament\Schemas\Schema;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Actions\EditAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use App\Filament\Backoffice\Resources\PullZoneResource\Pages\ListPullZones;
use App\Filament\Backoffice\Resources\PullZoneResource\Pages\CreatePullZone;
use App\Filament\Backoffice\Resources\PullZoneResource\Pages\EditPullZone;
use App\Filament\Backoffice\Resources\PullZoneResource\Pages;
use App\Filament\Backoffice\Resources\PullZoneResource\RelationManagers;
use App\Models\PullZone;
use App\Models\PullZoneStatus;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Number;

class PullZoneResource extends Resource
{
    protected static ?string $model = PullZone::class;

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-arrow-down-on-square';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('handle')
                    ->rules(['required', 'string', 'max:64', 'unique:pull_zones,handle,{{resourceId}}']),
                TextInput::make('custom_hostname')
                    ->rules(['nullable', 'string', 'max:255']),
                Select::make('status')
                    ->options(PullZoneStatus::class),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('handle')->searchable()->copyable(),
                TextColumn::make('custom_hostname')->copyable(),
                TextColumn::make('bandwidth_limit')
                    ->label('Bandwidth Limit')
                    ->state(fn (PullZone $record) => Number::fileSize((int)$record->monthlyBandwidthLimit()))
                    ->alignRight(),
                TextColumn::make('status')->badge(),
                TextColumn::make('user.name'),
                TextColumn::make('id_bunny')->searchable(),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => ListPullZones::route('/'),
            'create' => CreatePullZone::route('/create'),
            'edit'   => EditPullZone::route('/{record}/edit'),
        ];
    }
}
