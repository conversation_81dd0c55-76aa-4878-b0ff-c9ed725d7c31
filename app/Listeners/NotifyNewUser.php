<?php

namespace App\Listeners;

use App\Mail\WelcomeEmail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class NotifyNewUser implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        $user = $event?->user ?? $event?->getUser();
        if (! $user) {
            return;
        }

        // Send ping notification
        ping('⛅ Skymage, yo, we got a new user: ' . $user->email);

        // Send welcome email
        Mail::to($user)->send(new WelcomeEmail($user));
    }
}
