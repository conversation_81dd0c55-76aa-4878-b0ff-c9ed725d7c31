<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum ExternalStorageDriver: string implements <PERSON><PERSON>abel, HasIcon, HasColor
{
    case Bunny = 'bunny';
    case S3 = 's3';
    case FTP = 'ftp';

    public function getLabel(): string
    {
        return match ($this) {
            self::Bunny => __('Bunny Storage'),
            self::S3 => __('Amazon S3'),
            self::FTP => __('FTP'),
        };
    }

    public function getIcon(): string
    {
        return match ($this) {
            self::Bunny => 'heroicon-o-cloud',
            self::S3 => 'heroicon-o-server-stack',
            self::FTP => 'heroicon-o-folder',
        };
    }

    public function getColor(): string
    {
        return match ($this) {
            self::Bunny => 'orange',
            self::S3 => 'blue',
            self::FTP => 'gray',
        };
    }
}
