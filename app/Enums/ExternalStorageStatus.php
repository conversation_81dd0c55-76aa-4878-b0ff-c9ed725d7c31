<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum ExternalStorageStatus: string implements HasLabel, HasColor, HasIcon
{
    case Connected = 'connected';
    case Disconnected = 'disconnected';

    public function getLabel(): string
    {
        return match ($this) {
            self::Connected => __('Connected'),
            self::Disconnected => __('Disconnected'),
        };
    }

    public function getColor(): string
    {
        return match ($this) {
            self::Connected => 'success',
            self::Disconnected => 'gray',
        };
    }

    public function getIcon(): string
    {
        return match ($this) {
            self::Connected => 'heroicon-o-check-circle',
            self::Disconnected => 'heroicon-o-x-circle',
        };
    }
}
