<?php

namespace App\Billing;

use Illuminate\Support\Facades\Cache;
use LemonSqueezy\Laravel\Billable;
use LemonSqueezy\Laravel\LemonSqueezy;

class Plan
{
    public string $id;

    public string $name;

    public ?string $shortDescription = '';

    public ?int $price = 0;

    public string $interval = 'monthly';

    public bool $active = true;

    public array $features = [];

    public array $options = [];

    public function __construct(string $name, string $id)
    {
        $this->id = $id;

        $this->name = $name;
    }

    public function price(?int $price): self
    {
        $this->price = $price;

        return $this;
    }

    public function monthly(): self
    {
        $this->interval = 'monthly';

        return $this;
    }

    public function yearly(): self
    {
        $this->interval = 'yearly';

        return $this;
    }

    public function shortDescription(?string $shortDescription): self
    {
        $this->shortDescription = $shortDescription;

        return $this;
    }

    public function archived(): self
    {
        $this->active = false;

        return $this;
    }

    public function features(array $features): self
    {
        $this->features = $features;

        return $this;
    }

    public function options(array $options): self
    {
        $this->options = array_merge($this->options, $options);

        return $this;
    }

    public function isMonthly(): bool
    {
        return $this->interval === 'monthly';
    }

    public function isYearly(): bool
    {
        return $this->interval === 'yearly';
    }

    public function isFreePlan(): bool
    {
        return str_contains($this->id, 'free');
    }

    public function getPrice(): int
    {
        if ($this->isFreePlan()) {
            return 0;
        }

        if ($this->price) {
            return $this->price;
        }

        // get price from api

        $price = Cache::remember('billing-plan-' . $this->id, 60 * 60 * 24, function () {
            return $this->getPriceFromLemonSqueezy();
        });

        return $price;
    }

    protected function getPriceFromLemonSqueezy(): int
    {
        $response = LemonSqueezy::api('GET', 'variants/' . $this->id);

        $price = $response['data']['attributes']['price'] / 100;

        return $price;
    }

    public function isPopular(): bool
    {
        return $this->name === 'Growth';
    }
}
