<?php

namespace App\Billing;

class Billing
{
    public static array $plans = [];

    public static array $developers = [];

    public static function plan(string $name, string $id): Plan
    {
        static::$plans[] = $plan = new Plan($name, $id);

        return $plan;
    }

    public static function findPlan($planId): ?Plan
    {
        return collect(static::$plans)->first(fn(Plan $plan) => $plan->id === $planId);
    }

    /**
     * @return array<Plan>
     */
    public static function activePlans(): array
    {
        return array_filter(static::$plans, fn(Plan $plan) => $plan->active);
    }

    public static function isDeveloper(string $email): bool
    {
        return in_array($email, static::$developers);
    }
}
