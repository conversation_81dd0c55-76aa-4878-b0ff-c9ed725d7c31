<?php

namespace App\Billing;

use Illuminate\Support\ServiceProvider;

class BillingServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->configurePlans();
        $this->configureDevelopers();
    }

    protected function configureDevelopers(): void
    {
        Billing::$developers = config('billing.developers');
    }

    protected function configurePlans(): void
    {
        foreach (config('billing.plans') as $plan) {
            if ($plan['monthly_id'] ?? false) {
                Billing::plan($plan['name'], $plan['monthly_id'])
                    ->shortDescription($plan['short_description'] ?? null)
                    ->price($plan['price'] ?? 0)
                    ->monthly()
                    ->options($plan['options'] ?? [])
                    ->features($plan['features']);
            }

            if ($plan['yearly_id'] ?? false) {
                Billing::plan($plan['name'], $plan['yearly_id'])
                    ->shortDescription($plan['short_description'] ?? null)
                    ->price($plan['price'] ?? 0)
                    ->yearly()
                    ->options($plan['options'] ?? [])
                    ->features($plan['features']);
            }
        }
    }
}
