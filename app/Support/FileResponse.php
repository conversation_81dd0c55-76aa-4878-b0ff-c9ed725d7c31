<?php

namespace App\Support;

use Carbon\Carbon;

class FileResponse
{
    public function __construct(
        public readonly string $content,
        public readonly string $contentType,
        public readonly int $size,
        public readonly ?Carbon $lastModified = null,
        public readonly ?string $etag = null,
        public readonly array $headers = []
    ) {
    }

    public function getContent(): string
    {
        return $this->content;
    }

    public function getContentType(): string
    {
        return $this->contentType;
    }

    public function getSize(): int
    {
        return $this->size;
    }

    public function getLastModified(): ?Carbon
    {
        return $this->lastModified;
    }

    public function getEtag(): ?string
    {
        return $this->etag;
    }

    public function getHeaders(): array
    {
        return $this->headers;
    }

    public function getHeader(string $name, ?string $default = null): ?string
    {
        return $this->headers[$name] ?? $default;
    }

    public function isImage(): bool
    {
        return str_starts_with($this->contentType, 'image/');
    }

    public function isText(): bool
    {
        return str_starts_with($this->contentType, 'text/');
    }

    public function isPdf(): bool
    {
        return $this->contentType === 'application/pdf';
    }
}

/*
Example usage of the FileResponse:

$storage = ExternalStorage::find(1);
$file = $storage->getFile('images/logo.png');

if ($file) {
    // Access file content
    $content = $file->getContent();

    // Check content type for proper handling
    if ($file->isImage()) {
        return response($content, 200, [
            'Content-Type' => $file->getContentType(),
            'Content-Length' => $file->getSize(),
        ]);
    }

    // Access metadata
    $lastModified = $file->getLastModified();
    $etag = $file->getEtag();
    $customHeader = $file->getHeader('X-Custom-Header');

    // Use for caching
    if ($etag && request()->header('If-None-Match') === $etag) {
        return response('', 304);
    }
}
*/
