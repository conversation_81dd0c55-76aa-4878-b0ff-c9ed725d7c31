<?php

namespace App\Support;

class SkymageCDN
{
    /**
     * The base URL for the Skymage CDN.
     */
    protected string $cdnBaseUrl = 'https://demo.skymage.net/v1/';

    /**
     * Create a new SkymageCDN instance with optional custom base URL.
     */
    public function __construct(?string $cdnBaseUrl = null)
    {
        if ($cdnBaseUrl) {
            $this->cdnBaseUrl = $cdnBaseUrl;
        }
    }

    /**
     * Transform an image URL to use the Skymage CDN.
     */
    public function transfer(string $imageUrl, array $options = []): string
    {
        // Parse the URL to remove the protocol
        $parsedUrl = parse_url($imageUrl);

        if (!isset($parsedUrl['host'])) {
            return $imageUrl; // Return original if not a valid URL
        }

        // Build the path without protocol
        $path = $parsedUrl['host'];
        if (isset($parsedUrl['path'])) {
            $path .= $parsedUrl['path'];
        }

        // Build query string from options
        $query = '';
        if (!empty($options)) {
            $query = '?' . http_build_query($options);
        }

        return $this->cdnBaseUrl . '/' . $path . $query;
    }
}
