<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class Cloudflare
{
    protected string $url = 'https://api.cloudflare.com/client/v4';

    public function __construct(protected string $token)
    {
    }

    public function request(string $method, string $endpoint, array $payload = []): array
    {
        $response = Http::withToken($this->token)
            ->$method($this->url . Str::start('/', $endpoint), $payload);

        $response->throw();

        return $response->json();
    }

    public function createDnsRecord(string $zoneId, array $payload): array
    {
        return $this->request('post', "/zones/$zoneId/dns_records", $payload);
    }

    public function listZones(array $payload = []): array
    {
        return $this->request('get', '/zones', $payload);
    }
}
