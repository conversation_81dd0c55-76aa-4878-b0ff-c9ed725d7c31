<?php

namespace App\Services\StorageDrivers;

use Exception;
use App\Contracts\ExternalStorageDriverInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class S3StorageDriver implements ExternalStorageDriverInterface
{
    protected string $accessKeyId;
    protected string $secretAccessKey;
    protected string $bucket;
    protected string $region;
    protected ?string $endpoint;

    public function __construct(array $config)
    {
        $this->accessKeyId = $config['access_key_id'] ?? '';
        $this->secretAccessKey = $config['secret_access_key'] ?? '';
        $this->bucket = $config['bucket'] ?? '';
        $this->region = $config['region'] ?? 'us-east-1';
        $this->endpoint = $config['endpoint'] ?? null;
    }

    public function isValid(): array
    {
        try {
            // Validate required configuration
            if (empty($this->accessKeyId)) {
                return [
                    'success' => false,
                    'message' => 'Access Key ID is required but not provided'
                ];
            }

            if (empty($this->secretAccessKey)) {
                return [
                    'success' => false,
                    'message' => 'Secret Access Key is required but not provided'
                ];
            }

            if (empty($this->bucket)) {
                return [
                    'success' => false,
                    'message' => 'Bucket name is required but not provided'
                ];
            }

            // Test connection by performing a HEAD request on the bucket
            $url = $this->buildUrl();
            $headers = $this->buildAuthHeaders('HEAD', '/', '');

            Log::debug('S3 Connection Test', [
                'url' => $url,
                'headers' => array_keys($headers), // Don't log sensitive values
                'bucket' => $this->bucket,
                'region' => $this->region,
                'endpoint' => $this->endpoint
            ]);

            $response = Http::withHeaders($headers)->head($url);

            if (!$response->successful()) {
                $statusCode = $response->status();
                $responseBody = $response->body();

                Log::warning('S3 Connection Failed', [
                    'status_code' => $statusCode,
                    'response_body' => $responseBody,
                    'url' => $url,
                    'bucket' => $this->bucket,
                    'response_headers' => $response->headers()
                ]);

                // Handle region mismatch (301 redirect or 400 with region header)
                $correctRegion = null;
                if ($statusCode === 301) {
                    $correctRegion = $this->extractRegionFromRedirect($response);
                } elseif ($statusCode === 400) {
                    // Check for x-amz-bucket-region header in 400 responses
                    $bucketRegionHeader = $response->header('x-amz-bucket-region');
                    if ($bucketRegionHeader) {
                        $correctRegion = $bucketRegionHeader;
                    }
                }

                $regionMessage = null;
                if ($correctRegion && $correctRegion !== $this->region) {
                    $regionMessage = "The bucket '{$this->bucket}' is in region '{$correctRegion}', but you configured '{$this->region}'. Please update your region configuration to '{$correctRegion}'.";
                }

                return [
                    'success' => false,
                    'message' => match ($statusCode) {
                        403 => 'Access denied - Check your credentials and bucket permissions',
                        404 => "Bucket '{$this->bucket}' not found or access denied",
                        401 => 'Invalid credentials - Authentication failed',
                        400 => $regionMessage ?? "Bad request - Check your configuration. Response: {$responseBody}",
                        301 => $regionMessage ?? "Bucket region mismatch - The bucket '{$this->bucket}' is not in region '{$this->region}'. Check the bucket's actual region or update your configuration.",
                        500, 502, 503, 504 => 'S3 server error - Try again later',
                        default => "Connection failed with HTTP {$statusCode}. Response: {$responseBody}"
                    }
                ];
            }

            return [
                'success' => true,
                'message' => 'Connection successful'
            ];
        } catch (Exception $e) {
            report($e);

            return [
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Build the S3 URL for the bucket
     */
    private function buildUrl(): string
    {
        if ($this->endpoint) {
            // Custom endpoint (e.g., DigitalOcean Spaces, MinIO)
            $baseUrl = rtrim($this->endpoint, '/');

            // Check if endpoint already includes protocol
            if (!str_starts_with($baseUrl, 'http://') && !str_starts_with($baseUrl, 'https://')) {
                $baseUrl = "https://{$baseUrl}";
            }

            // For path-style access (common with custom endpoints)
            return "{$baseUrl}/{$this->bucket}";
        }

        // AWS S3 standard endpoint - use virtual-hosted style
        // For us-east-1, use the legacy endpoint format
        if ($this->region === 'us-east-1') {
            return "https://{$this->bucket}.s3.amazonaws.com";
        }

        return "https://{$this->bucket}.s3.{$this->region}.amazonaws.com";
    }

    /**
     * Build authentication headers for S3 API requests
     */
    private function buildAuthHeaders(string $method, string $path, string $payload): array
    {
        $timestamp = gmdate('Ymd\THis\Z');
        $date = gmdate('Ymd');
        $host = parse_url($this->buildUrl(), PHP_URL_HOST);

        // Calculate payload hash (required for AWS Signature Version 4)
        $payloadHash = hash('sha256', $payload);

        // Normalize path - ensure it starts with /
        $canonicalPath = $path === '/' ? '/' : '/' . ltrim($path, '/');

        // Create canonical headers (must be sorted alphabetically)
        // Note: x-amz-content-sha256 is required for all AWS Signature Version 4 requests
        $canonicalHeaders = "host:{$host}\n";
        $canonicalHeaders .= "x-amz-content-sha256:{$payloadHash}\n";
        $canonicalHeaders .= "x-amz-date:{$timestamp}\n";
        $signedHeaders = 'host;x-amz-content-sha256;x-amz-date';

        $canonicalRequest = implode("\n", [
            $method,
            $canonicalPath,
            '', // query string (empty for bucket HEAD request)
            $canonicalHeaders,
            $signedHeaders,
            $payloadHash
        ]);

        // Create string to sign
        $credentialScope = "{$date}/{$this->region}/s3/aws4_request";
        $stringToSign = implode("\n", [
            'AWS4-HMAC-SHA256',
            $timestamp,
            $credentialScope,
            hash('sha256', $canonicalRequest)
        ]);

        // Calculate signature
        $signature = $this->calculateSignature($stringToSign, $date);

        // Debug logging for signature calculation
        Log::debug('S3 Signature Calculation', [
            'canonical_request' => $canonicalRequest,
            'string_to_sign' => $stringToSign,
            'credential_scope' => $credentialScope,
            'signed_headers' => $signedHeaders,
            'payload_hash' => $payloadHash
        ]);

        // Build authorization header
        $authorization = "AWS4-HMAC-SHA256 " .
            "Credential={$this->accessKeyId}/{$credentialScope}, " .
            "SignedHeaders={$signedHeaders}, " .
            "Signature={$signature}";

        return [
            'Authorization' => $authorization,
            'X-Amz-Content-Sha256' => $payloadHash,
            'X-Amz-Date' => $timestamp,
            'Host' => $host
        ];
    }

    /**
     * Calculate AWS Signature Version 4
     */
    private function calculateSignature(string $stringToSign, string $date): string
    {
        $kDate = hash_hmac('sha256', $date, 'AWS4' . $this->secretAccessKey, true);
        $kRegion = hash_hmac('sha256', $this->region, $kDate, true);
        $kService = hash_hmac('sha256', 's3', $kRegion, true);
        $kSigning = hash_hmac('sha256', 'aws4_request', $kService, true);

        return hash_hmac('sha256', $stringToSign, $kSigning);
    }

    /**
     * Extract the correct region from a 301 redirect response
     */
    private function extractRegionFromRedirect($response): ?string
    {
        $location = $response->header('Location');
        if (!$location) {
            return null;
        }

        // Parse the redirect URL to extract region
        // Example: https://bucket-name.s3.eu-west-1.amazonaws.com/
        if (preg_match('/\.s3\.([a-z0-9-]+)\.amazonaws\.com/', $location, $matches)) {
            return $matches[1];
        }

        // Handle us-east-1 special case
        if (preg_match('/\.s3\.amazonaws\.com/', $location)) {
            return 'us-east-1';
        }

        return null;
    }
}
