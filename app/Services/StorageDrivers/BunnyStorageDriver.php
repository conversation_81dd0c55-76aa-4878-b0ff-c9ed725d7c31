<?php

namespace App\Services\StorageDrivers;

use Exception;
use App\Contracts\ExternalStorageDriverInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BunnyStorageDriver implements ExternalStorageDriverInterface
{
    protected string $apiKey;
    protected string $storageZone;
    protected string $region;

    public function __construct(array $config)
    {
        $this->apiKey = $config['api_key'] ?? '';
        $this->storageZone = $config['storage_zone'] ?? '';
        $this->region = $config['region'] ?? 'de';
    }
    public function isValid(): array
    {
        try {
            // Validate required configuration
            if (empty($this->apiKey)) {
                return [
                    'success' => false,
                    'message' => 'API Key is required but not provided'
                ];
            }

            if (empty($this->storageZone)) {
                return [
                    'success' => false,
                    'message' => 'Storage Zone is required but not provided'
                ];
            }

            // Test connection by listing files in the storage zone root
            // Using the storage zone specific API endpoint as per Bunny Storage docs:
            // https://docs.bunny.net/reference/get_-storagezonename-path-
            $url = match ($this->region) {
                'de' => "https://storage.bunnycdn.com/{$this->storageZone}/",
                default => "https://{$this->region}.storage.bunnycdn.com/{$this->storageZone}/",
            };

            $response = Http::withHeaders([
                'AccessKey' => $this->apiKey,
                'Accept' => 'application/json',
            ])->get($url);

            if (!$response->successful()) {
                $statusCode = $response->status();
                return [
                    'success' => false,
                    'message' => match ($statusCode) {
                        401 => 'Invalid API Key - Authentication failed',
                        403 => 'Access denied - Check your API Key permissions',
                        404 => "Storage zone '{$this->storageZone}' not found or access denied",
                        429 => 'Rate limit exceeded - Too many requests',
                        500, 502, 503, 504 => 'Bunny Storage server error - Try again later',
                        default => "Connection failed with HTTP {$statusCode}"
                    }
                ];
            }

            // If we can successfully list the root directory, the connection is valid
            return [
                'success' => true,
                'message' => 'Connection successful'
            ];
        } catch (Exception $e) {
            report($e);

            return [
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ];
        }
    }
}
