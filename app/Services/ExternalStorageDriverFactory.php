<?php

namespace App\Services;

use Exception;
use App\Contracts\ExternalStorageDriverInterface;
use App\Enums\ExternalStorageDriver;
use App\Models\ExternalStorage;
use App\Services\StorageDrivers\BunnyStorageDriver;
use App\Services\StorageDrivers\S3StorageDriver;

class ExternalStorageDriverFactory
{
    public static function create(ExternalStorage $storage): ExternalStorageDriverInterface
    {
        return match ($storage->driver) {
            ExternalStorageDriver::Bunny => new BunnyStorageDriver($storage->configuration),
            ExternalStorageDriver::S3 => new S3StorageDriver($storage->configuration),
            ExternalStorageDriver::FTP => throw new Exception('FTP driver not implemented yet'),
        };
    }
}
