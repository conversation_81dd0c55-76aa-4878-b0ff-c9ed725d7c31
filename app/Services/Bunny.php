<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class Bunny
{
    protected string $url = 'https://api.bunny.net';

    public function __construct(protected string $apiKey)
    {
    }

    public function request(string $method, string $endpoint, array $payload = []): array|null
    {
        $endpoint = ltrim($endpoint, '/'); // Ensure endpoint does not start with a slash

        $response = Http::withHeader('AccessKey', $this->apiKey)
            ->throw()
            ->acceptJson()
            ->$method("{$this->url}/$endpoint", $payload);

        return $response->json();
    }

    /**
     * Add a pull zone to Bunny CDN
     */
    public function addPullZone(array $payload): array
    {
        return $this->request('post', '/pullzone', $payload);
    }

    public function getPullZone(int $id): array
    {
        return $this->request('get', '/pullzone/' . $id);
    }

    /**
     * Add custom hostname to a pull zone
     */
    public function addHostName(string $pullZoneId, string $hostname): null
    {
        return $this->request('post', "/pullzone/$pullZoneId/addHostname", [
            'Hostname' => $hostname,
        ]);
    }

    /**
     * Load a free certificate for a hostname, Let's Encrypt, tho
     */
    public function loadFreeCertificate(string $hostname): void
    {
        $this->request('get', '/pullzone/loadFreeCertificate', [
            'Hostname' => $hostname,
        ]);
    }

    public function purge(string $url): void
    {
        $this->request('get', '/purge', [
            'url' => $url
        ]);
    }

    public function purgeCache(string $pullZoneId): void
    {
        $this->request('post', "/pullzone/$pullZoneId/purgeCache");
    }

    public function statistics(array $payload = []): array
    {
        return $this->request('get', '/statistics', $payload);
    }

    public function logs(string $date, string $pullZoneId, int $start = 0, int $end = 50): string
    {
        $response = Http::withHeaders([
            'Accept' => 'application/json, text/plain, */*',
            'AccessKey' => $this->apiKey,
        ])->get("https://logging.bunnycdn.com/{$date}/{$pullZoneId}.log", [
            'start' => $start,
            'end' => $end,
        ]);

        if (!$response->successful()) {
            return '';
        }

        return $response->body();
    }

    public function updatePullZone(string $pullZoneId, array $payload = []): array
    {
        return $this->request('post', '/pullzone/' . $pullZoneId, $payload);
    }
}
