<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Symfony\Component\Yaml\Yaml;

class BlogPostParser
{
    /**
     * Get all blog posts with parsed frontmatter and content
     */
    public function getAllPosts(): Collection
    {
        $blogDir = resource_path('blog');
        $files = File::glob($blogDir . '/*.md');

        $posts = [];
        foreach ($files as $file) {
            $posts[] = $this->parseFromFile($file);
        }

        return collect($posts)->sortByDesc('date')->values();
    }

    /**
     * Get published blog posts (dated today or earlier)
     */
    public function getPublishedPosts(): Collection
    {
        return $this->getAllPosts()->filter(function (array $post): bool {
            return $post['date']->startOfDay()->lte(now()->startOfDay());
        })->values();
    }

    /**
     * Get a post by its slug
     */
    public function getPostBySlug(string $slug): ?array
    {
        $blogDir = resource_path('blog');
        $files = File::glob($blogDir . '/*-' . $slug . '.md');

        if (empty($files)) {
            return null;
        }

        // Get the most recent file if multiple matches are found
        $blogFile = $files[0];
        return $this->parseFromFile($blogFile);
    }

    /**
     * Parse a single blog post file into a structured array
     */
    public function parseFromFile(string $filePath): array
    {
        $fileName = basename($filePath);
        $datePart = substr($fileName, 0, 10);
        $date = Carbon::createFromFormat('Y-m-d', $datePart);
        $slug = str_replace('.md', '', str_replace($datePart . '-', '', $fileName));

        // Get file content
        $fileContent = File::get($filePath);

        // Parse frontmatter and content
        [$frontmatter, $content] = $this->parseFrontmatterAndContent($fileContent);

        // Return structured post data
        return [
            'title' => $frontmatter['title'] ?? 'Untitled',
            'slug' => $slug,
            'date' => $date,
            'description' => $frontmatter['description'] ?? '',
            'featured_image' => $frontmatter['featured_image'] ?? '',
            'content' => $content,
            'raw_content' => $content,
            'frontmatter' => $frontmatter,
        ];
    }

    /**
     * Separates and parses frontmatter and content from a markdown file
     *
     * @return array{0: array, 1: string} Array containing frontmatter and content
     */
    protected function parseFrontmatterAndContent(string $fileContent): array
    {
        $frontmatter = [];
        $content = $fileContent;

        // Check if the markdown has frontmatter (enclosed in ---)
        if (preg_match('/^---\s*\n(.*?)\n---\s*\n(.*)/s', $fileContent, $matches)) {
            try {
                $frontmatter = Yaml::parse($matches[1]);
            } catch (Exception $e) {
                // If YAML parsing fails, fallback to simple key-value extraction
                $frontmatter = $this->parseFrontmatterWithFallback($matches[1]);
            }
            $content = $matches[2];
        }

        return [$frontmatter, $content];
    }

    /**
     * Parse frontmatter using a fallback method when YAML parsing fails
     */
    protected function parseFrontmatterWithFallback(string $frontmatterText): array
    {
        $frontmatter = [];
        preg_match_all('/^([^:]+):\s*(.+)$/m', $frontmatterText, $pairs, PREG_SET_ORDER);

        foreach ($pairs as $pair) {
            $frontmatter[trim($pair[1])] = trim($pair[2]);
        }

        return $frontmatter;
    }

    /**
     * Get the latest n posts
     */
    public function getLatestPosts(int $count = 3): Collection
    {
        return $this->getPublishedPosts()->take($count);
    }
}