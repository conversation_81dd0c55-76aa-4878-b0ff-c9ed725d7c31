<?php

namespace App\Livewire\Auth;

use Filament\Http\Responses\Auth\Contracts\RegistrationResponse;
use Filament\Support\Facades\FilamentView;
use Filament\View\PanelsRenderHook;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;

/** @package App\Livewire\Auth */
class Register extends \Filament\Auth\Pages\Register
{
    public function mount(): void
    {
        parent::mount();

        FilamentView::registerRenderHook(
            PanelsRenderHook::AUTH_REGISTER_FORM_BEFORE,
            fn () => new HtmlString(Blade::render("<x-auth.social-login-buttons />"))
        );

        FilamentView::registerRenderHook(
            PanelsRenderHook::AUTH_LOGIN_FORM_AFTER,
            fn() => new HtmlString("<div class='text-sm'>After you register, you will be redirected to the checkout page.</div>")
        );
    }
}
