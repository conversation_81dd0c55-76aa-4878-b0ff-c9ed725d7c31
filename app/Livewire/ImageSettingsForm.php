<?php

namespace App\Livewire;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Tabs;
use Filament\Schemas\Components\Tabs\Tab;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Components\Section;
use App\Models\PullZone;
use App\Models\User;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Notifications\Notification;
use Filament\Schemas\Concerns\InteractsWithSchemas;
use Filament\Schemas\Contracts\HasSchemas;
use Livewire\Component;
use Illuminate\Contracts\View\View;

class ImageSettingsForm extends Component implements HasSchemas, HasActions
{
    use InteractsWithSchemas;
    use InteractsWithActions;

    public ?array $data = [];

    public User $user;

    public ?PullZone $pullZone;

    public function mount(): void
    {
        $this->form->fill(array_merge([
            'enable_domains_allow_list' => false,
        ], $this->pullZone->settings ?? []));
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->statePath('data')
            ->schema([
                Tabs::make()
                    ->persistTabInQueryString()
                    ->tabs([
                        $this->domainsTab(),
                        $this->compressionTab(),
                        $this->headersTab(),
                        $this->watermarkTab(),
                    ]),

                Action::make('save')
                    ->label(__('Save changes'))
                    ->action($this->save(...))
            ]);
    }

    protected function domainsTab(): Tab
    {
        return Tab::make('domains')->label('Domains')
            ->schema([
                Toggle::make('enable_domains_allow_list')
                    ->inline(false)
                    ->label(__('Enable domains allow list'))
                    ->helperText(__('Only images from the domains listed below will be processed.'))
                    ->live(),
                Repeater::make('domains')
                    ->hint(__('Please enter the domain name without the protocol (http:// or https://)'))
                    ->reorderable(false)
                    ->simple(
                        TextInput::make('domain')
                            ->label(__('Domain'))
                            ->placeholder('example.com')
                            ->required(fn(Get $get) => !!$get('enable_domains_allow_list'))
                            ->rules(['string', 'max:250', 'regex:/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/']),
                    ),
            ]);
    }

    protected function compressionTab(): Tab
    {
        return Tab::make('compression')->label('Compression')
            ->schema([
                Section::make('')
                    ->heading(__('Compression quantity'))
                    ->description(__('The quality of the image compression. A higher value will result in better quality but larger file sizes.'))
                    ->schema([
                        TextInput::make('jpeg_compression')
                            ->label('Compression quality')
                            ->maxWidth('200px')
                            ->numeric()
                            ->default(85)
                            ->maxValue(100)
                            ->integer()
                            ->minValue(10),
                    ]),

                Section::make()
                    ->heading('WebP')
                    ->description('WebP is an image format supported by most modern browsers. It achieves better compression than JPEG and is optimized for web delivery.')
                    ->schema([
                        Toggle::make('enable_webp')
                            ->label(__('Enable WebP'))
                            ->default(false)
                            ->helperText('If enabled, supported images will be converted to WebP format. This can result in smaller file sizes and faster load times.'),
                        Toggle::make('webp_lossless')
                            ->label(__('Use lossless compression'))
                            ->hint('Not recommended for most use cases.')
                            ->helperText('If enabled, Skymage will use lossless WebP compression. which will result in larger file sizes but better quality.')
                            ->default(false),
                    ])

            ]);
    }

    protected function headersTab(): Tab
    {
        return Tab::make('headers')
            ->label('Headers')
            ->schema([
                Toggle::make('add_canonical_header')
                    ->label(__('Add canonical header'))
                    ->helperText(__('If enabled, Skymage will automatically add a Canonical link header
                     to your requests pointing back to your origin URL. Useful for SEO optimization.')),
            ]);
    }

    protected function watermarkTab(): Tab
    {
        return Tab::make('watermark')
            ->label('Watermark')
            ->schema([
                Toggle::make('enable_domain_watermark')
                    ->label(__('Enable domain watermark'))
                    ->helperText(__('If enabled, Skymage will automatically add a watermark to your images
                     with the domain name of the request at the bottom right of image. You may need to purge your cache after enabling this feature.')),
            ]);
    }

    public function save(): void
    {
        $data = $this->form->getState();

        $this->pullZone->update([
            'settings' => $data,
        ]);

        Notification::make()
            ->title(__('Settings saved'))
            ->success()
            ->send();
    }

    public function render(): View
    {
        return view('livewire.image-settings-form');
    }
}
