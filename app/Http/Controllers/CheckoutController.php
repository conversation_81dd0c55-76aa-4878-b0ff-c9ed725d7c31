<?php

namespace App\Http\Controllers;

use App\Billing\Billing;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Models\User;

class CheckoutController extends Controller
{
    public function create(Request $request, string $planId)
    {
        $plan = Billing::findPlan($planId);
        /* @var User $user */
        $user = $request->user();

        if (!$user) {
            if (!$plan->isFreePlan()) {
                Session::put('url.intended', route('checkout.create', $planId));
            }

            return redirect()->route('filament.dashboard.auth.register');
        }

        return $user
            ->subscribe(variant: $planId)
            ->redirectTo(route('filament.dashboard.pages.dashboard'));
    }
}
