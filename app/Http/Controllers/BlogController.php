<?php

namespace App\Http\Controllers;

use App\Services\BlogPostParser;
use Illuminate\Http\Response;

class BlogController extends Controller
{
    protected BlogPostParser $blogPostParser;

    public function __construct(BlogPostParser $blogPostParser)
    {
        $this->blogPostParser = $blogPostParser;
    }

    public function index()
    {
        return view('blog.index', [
            'posts' => $this->blogPostParser->getPublishedPosts()->all(),
        ]);
    }

    public function show(string $slug)
    {
        $post = $this->blogPostParser->getPostBySlug($slug);

        if (!$post) {
            abort(404, 'Blog post not found');
        }

        return view('blog.show', [
            'content' => $post['content'],
            'frontmatter' => $post['frontmatter'],
            'title' => $post['title'],
            'description' => $post['description'],
            'featured_image' => $post['featured_image'],
            'date' => $post['date'],
            'slug' => $post['slug']
        ]);
    }
}
