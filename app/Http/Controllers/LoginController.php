<?php

namespace App\Http\Controllers;

use App\Enums\LoginSocial;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class LoginController extends Controller
{
    /**
     * Redirect the user to the provider authentication page.
     */
    public function login(LoginSocial $social)
    {
        return Socialite::driver($social->value)->redirect();
    }

    /**
     * Handle the provider callback and authenticate the user.
     */
    public function callback(LoginSocial $social)
    {
        $socialUser = Socialite::driver($social->value)->user();

        $user = User::firstOrCreate([
            'email' => $socialUser->getEmail(),
        ], [
            'name'     => $socialUser->getName(),
            'password' => Hash::make(Str::random(72)),
        ]);

        if ($user->wasRecentlyCreated) {
            $user->update([
                'meta' => [
                    'social_avatar'        => $socialUser->getAvatar(),
                    'id_' . $social->value => $socialUser->getId(),
                ]
            ]);

            event(new Registered($user));
        }

        Auth::login($user, true);

        return redirect('/dashboard');
    }
}
