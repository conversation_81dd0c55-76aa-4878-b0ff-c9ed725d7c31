<?php

namespace App\Http\Middleware;

use App\Billing\Billing;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VerifyBillableSubscribed
{
    /**
     * Handle an incoming request.
     *
     * @param Closure(Request):Response $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->user()->onTrial() || $request->user()->subscribed() || Billing::isDeveloper($request->user()->email)) {
            return $next($request);
        }

        return redirect()->to('/billing');
    }
}
