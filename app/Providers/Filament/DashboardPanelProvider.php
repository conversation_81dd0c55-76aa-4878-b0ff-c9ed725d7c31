<?php

namespace App\Providers\Filament;

use Filament\Support\Enums\Width;
use Filament\Schemas\Components\Section;
use App\Filament\Pages\Billing;
use App\Filament\Pages\Dashboard;
use App\Filament\Pages\Logs;
use App\Filament\Pages\Purge;
use App\Filament\Pages\Settings;
use App\Filament\Pages\Statistics;
use App\Livewire\Auth\Login;
use App\Livewire\Auth\Register;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Support\Facades\FilamentView;
use Filament\Tables\Table;
use Filament\View\PanelsRenderHook;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class DashboardPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('dashboard')
            ->font('Figtree')
            ->path('/dashboard')
            ->homeUrl(config('app.url'))
            ->favicon(url('logo.svg'))
            ->brandLogo(url('logo.svg'))
            ->brandLogoHeight('50px')
            ->login(Login::class)
            ->registration(Register::class)
            ->revealablePasswords(false)
            ->profile()
            ->darkMode(false)
            ->breadcrumbs(false)
            ->maxContentWidth(Width::SevenExtraLarge)
            ->colors([
                'primary' => Color::Sky,
            ])
            ->viteTheme('resources/css/filament/app/theme.css')
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->pages([
                Dashboard::class,
                Settings::class,
                Purge::class,
                Statistics::class,
                Logs::class,
                Billing::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
            ])
            ->navigationGroups([
                'Delivery',
                'Analytics',
                'Others',
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
//                VerifyBillableSubscribed::class,
            ])
            ->spa(true);
    }

    public function register(): void
    {
        parent::register();
    }

    public function boot()
    {
        FilamentView::registerRenderHook(
            PanelsRenderHook::HEAD_END,
            fn () => view('lms-affiliate'),
        );

        FilamentView::registerRenderHook(
            PanelsRenderHook::BODY_END,
            fn () => view('live-chat'),
        );

        Table::configureUsing(function (Table $table) {
            $table->paginationPageOptions([15, 25, 50]);
        });

        Table::macro('minimal', fn() => $this->searchable(false)->paginated(false));

        $sectionClasses = implode(' ', [
            '[&>.fi-section-content-ctn]:!border-0'
        ]);

        Section::configureUsing(function (Section $section) use($sectionClasses) {
            $section->extraAttributes([
                'class' => $sectionClasses,
            ]);
        });

        Section::configureUsing(function (Section $section) use ($sectionClasses) {
            $section->extraAttributes([
                'class' => $sectionClasses,
            ]);
        });
    }
}
