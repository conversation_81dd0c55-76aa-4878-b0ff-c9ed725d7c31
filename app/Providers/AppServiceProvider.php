<?php

namespace App\Providers;

use Filament\Auth\Events\Registered;
use App\Actions\CreateFreeTrial;
use App\Actions\ProvisionCDN;
use App\Actions\SubscribeToPlan;
use App\Listeners\NotifyNewUser;
use App\Services\Bunny;
use App\Services\Cloudflare;
use App\Support\SkymageCDN;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Illuminate\Validation\Rules\Password;
use LemonSqueezy\Laravel\Events\SubscriptionCreated;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(Cloudflare::class, function () {
            return new Cloudflare(config('services.cloudflare.api_token'));
        });

        $this->app->bind(Bunny::class, function () {
            return new Bunny(config('services.bunny.api_key'));
        });

        // Register SkymageCDN as a singleton
        $this->app->singleton(SkymageCDN::class, function ($app) {
                return new SkymageCDN(config('services.skymage.cdn_url'));
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Password::defaults(fn() => Password::min(6));

        if (App::isProduction()) {
            URL::forceHttps();
        }

        $this->configureEvents();
    }

    protected function configureEvents(): void
    {
        Event::listen(
            SubscriptionCreated::class,
            SubscribeToPlan::class,
        );

        Event::listen(Registered::class, CreateFreeTrial::class);
        Event::listen(\Illuminate\Auth\Events\Registered::class, CreateFreeTrial::class);

        Event::listen(\Illuminate\Auth\Events\Registered::class, NotifyNewUser::class);
        Event::listen(Registered::class, NotifyNewUser::class);
    }
}
