<?php

namespace App\Actions;

use App\Actions\PullZone\AddHostNameToPullZone;
use App\Actions\PullZone\CreateBunnyPullZone;
use App\Models\PullZoneStatus;
use App\Models\User;
use Illuminate\Support\Facades\Bus;
use LemonSqueezy\Laravel\Events\SubscriptionCreated;
use Lorisleiva\Actions\Concerns\AsAction;
use App\Actions\PullZone\LoadFreeCertificate;

class ProvisionCDN
{
    use AsAction;

    public function handle(User $user, ?string $handle = null): void
    {
        $pullZone = $user->pullZone ?? $user->pullZone()->create([
            'status' => PullZoneStatus::Pending,
            'handle' => $handle
        ]);

        CreateBunnyPullZone::make()->handle($pullZone);

        Bus::chain([
            AddHostNameToPullZone::makeJob($pullZone),
            CreateCloudflareDomain::makeJob($pullZone),
            LoadFreeCertificate::makeJob($pullZone)->delay(now()->addMinute()), // delay after 1 minute to ensure the DNS record is propagated
            NotifyUserCdnProvisioned::makeJob($user),
        ])->dispatch();
    }

    public function asListener(SubscriptionCreated $event): void
    {
        if ($event->billable instanceof User) {
            $this->handle($event->billable);
        }
    }
}
