<?php

namespace App\Actions\PullZone;

use App\Models\PullZone;
use Lorisleiva\Actions\Concerns\AsObject;

class GeneratePullZoneHandle
{
    use AsObject;

    public static function generate(int $length = 7): string
    {
        $handle = static::generateHandle($length);

        while (PullZone::where('handle', $handle)->exists()) {
            $handle = static::generateHandle($length);
        }

        return $handle;
    }

    protected static function generateHandle(int $length = 7): string
    {
        // dictionary of characters to use in the handle
        $characters = 'abcdefghijklmnopqrstuvwxyz';

        // generate a string with $length characters
        $handle = '';

        for ($i = 0; $i < $length; $i++) {
            $handle .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $handle;
    }
}
