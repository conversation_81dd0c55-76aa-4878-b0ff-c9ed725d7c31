<?php

namespace App\Actions\PullZone;

use App\Models\PullZone;
use App\Services\Bunny;
use Lorisleiva\Actions\Concerns\AsAction;

class AddHostNameToPullZone
{
    use AsAction;

    public function __construct(protected Bunny $bunny)
    {
    }

    public function handle(PullZone $pullZone): void
    {
       if ($pullZone->custom_hostname) {
            return;
       }

        $hostname = $pullZone->handle . '.skymage.net';

        $this->bunny->addHostName(
            $pullZone->id_bunny, $hostname
        );

        $pullZone->update([
            'custom_hostname' => $hostname,
        ]);
    }
}
