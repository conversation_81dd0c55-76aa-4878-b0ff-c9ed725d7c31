<?php

namespace App\Actions\PullZone;

use App\Models\PullZone;
use App\Models\PullZoneStatus;
use App\Services\Bunny;
use Lorisleiva\Actions\Concerns\AsAction;

class LoadFreeCertificate
{
    use AsAction;

    public $jobTries = 3;

    public $jobBackoff = 60;

    public function __construct(protected Bunny $bunny)
    {
    }

    public function handle(PullZone $pullZone): void
    {
        $this->bunny->loadFreeCertificate($pullZone->custom_hostname);

        $pullZone->update([
            'status' => PullZoneStatus::Active,
        ]);
    }
}
