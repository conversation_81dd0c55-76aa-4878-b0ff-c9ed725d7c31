<?php

namespace App\Actions\PullZone;

use App\Models\PullZone;
use App\Services\Bunny;
use Lorisleiva\Actions\Concerns\AsAction;

class GetPullZoneStatistics
{
    use AsAction;

    public function handle(PullZone $pullZone, array $payload = [])
    {
        $bunny = app(Bunny::class);

        return $bunny->statistics([
            'pullZone' => $pullZone->id_bunny
        ] + $payload);
    }
}
