<?php

namespace App\Actions;

use Filament\Auth\Events\Registered;
use App\Models\User;
use Filament\Facades\Filament;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateFreeTrial
{
    use AsAction;

    public function handle(User $user)
    {
        $user->createAsCustomer([
            'trial_ends_at' => now()->addDays(14),
        ]);

        ProvisionCDN::dispatch($user);
    }

    public function asListener(Registered|\Illuminate\Auth\Events\Registered $event): void
    {
        $user = match (true) {
            $event instanceof Registered => $event->getUser(),
            $event instanceof \Illuminate\Auth\Events\Registered => $event->user,
        };

        $this->handle($user);
    }
}
