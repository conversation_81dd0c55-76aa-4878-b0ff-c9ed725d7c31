<?php

namespace App\Actions;

use App\Models\User;
use App\Services\Bunny;
use App\Services\BunnyService;
use LemonSqueezy\Laravel\Events\SubscriptionCreated;
use Lorisleiva\Actions\Concerns\AsAction;

class SubscribeToPlan
{
    use AsAction;

    public function handle(User $user): void
    {
        /** @var BunnyService $bunnyService */
        $bunny = app(Bunny::class);
        $pullZone = $user->pullZone;

        $bunny->updatePullZone($pullZone->id_bunny, [
            'MonthlyBandwidthLimit' => $user->bandwidthLimitInGb() * 1000 * 1000 * 1000,
        ]);

        $pullZone->syncBunnyData();
    }

    public function asListener(SubscriptionCreated $event): void
    {
        if ($event->billable instanceof User) {
            $this->handle($event->billable);
        }
    }
}
