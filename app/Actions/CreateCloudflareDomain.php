<?php

namespace App\Actions;

use App\Actions\PullZone\LoadFreeCertificate;
use App\Models\PullZone;
use App\Services\Cloudflare;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateCloudflareDomain
{
    use AsAction;

    public function __construct(protected Cloudflare $cloudflare)
    {
    }

    public function handle(PullZone $pullZone): void
    {
        $name = Str::before($pullZone->custom_hostname, '.skymage.net');

        $result = $this->cloudflare->createDnsRecord(
            config('services.cloudflare.skymage_zone_id'),
            [
                'type'    => 'CNAME',
                'name'    => $name, // name without the domain
                'content' => $pullZone->systemHostname(), // the bunny system host name
                'proxied' => false, // false to use Certificate from Bunny
                'comment' => !App::isProduction() ? 'test' : '',
            ]
        );

        $pullZone->update([
            'cf_dns_record_id' => $result['result']['id'],
        ]);
    }
}
