# Statistics Page - Filament v4 Implementation

This is a comprehensive statistics dashboard for monitoring Bunny CDN performance metrics using Filament v4 widgets.

## Features

### 📊 Statistics Overview Widget
- **Total Requests**: Number of requests served
- **Bandwidth Used**: Total data transferred (with automatic unit conversion)
- **Cache Hit Rate**: Percentage of cached responses
- **Average Response Time**: Origin server response time in milliseconds

### 📈 Interactive Charts

#### 1. Bandwidth Usage Chart
- Area chart showing bandwidth usage over time
- Displays both total bandwidth and cached bandwidth
- Data shown in MB for better readability

#### 2. Requests Served Chart
- Bar chart showing request statistics
- Breaks down total, cached, and uncached requests
- Hourly granularity for detailed insights

#### 3. Cache Hit Rate Chart
- Line chart showing cache performance over time
- Percentage-based metrics (0-100%)
- Helps identify cache optimization opportunities

#### 4. Origin Response Time Chart
- Line chart displaying server response times
- Measured in milliseconds
- Useful for identifying performance bottlenecks

## Date Range Filtering

- **From/To Date Pickers**: Filter all widgets by date range
- **URL Parameters**: Date filters are preserved in URLs for sharing
- **Auto-refresh**: Widgets update automatically when date range changes
- **Default Range**: Last 7 days

## Bunny CDN API Integration

The widgets use the Bunny CDN Statistics API with the following parameters:
- `pullZone`: Pull zone ID from user's profile
- `dateFrom`: Start date for statistics
- `dateTo`: End date for statistics
- `hourly`: Enables hourly data granularity

## Caching Strategy

- **30-minute cache**: All API calls are cached for performance
- **Unique cache keys**: Include pull zone ID and date range
- **Fallback data**: Sample data provided for development/testing

## File Structure

```
app/Filament/
├── Pages/
│   └── Statistic.php              # Main statistics page
└── Widgets/
    ├── StatisticsOverview.php     # Overview stats widget
    ├── BandwidthChart.php         # Bandwidth usage chart
    ├── RequestsChart.php          # Request statistics chart
    ├── CacheHitRateChart.php      # Cache performance chart
    └── OriginResponseTimeChart.php # Response time chart

resources/views/filament/pages/
└── statistic.blade.php           # Custom page template
```

## Usage

1. **Access**: Navigate to the "Statistics" page in your Filament panel
2. **Filter**: Use the date range picker to filter data
3. **Monitor**: View real-time CDN performance metrics
4. **Analyze**: Identify trends and optimization opportunities

## Configuration

Ensure your `.env` file contains:
```env
BUNNY_API_KEY=your_bunny_api_key_here
```

## Error Handling

- **API Failures**: Gracefully handled with sample data fallback
- **Missing Data**: Empty states with helpful messages
- **Network Issues**: Cached data prevents complete failures

## Performance Optimizations

- **Lazy Loading**: Widgets load asynchronously
- **Caching**: 30-minute cache reduces API calls
- **Efficient Queries**: Optimized data fetching
- **Background Updates**: Non-blocking widget refresh

## Customization

### Adding New Metrics
1. Create a new widget extending appropriate base class
2. Add to `getWidgets()` array in `Statistic.php`
3. Implement data fetching and chart configuration

### Styling
Widgets inherit Filament v4 theming and support:
- Custom colors per chart type
- Responsive layouts
- Dark/light mode compatibility

### API Extensions
The Bunny service wrapper supports additional endpoints:
- Pull zone management
- Cache purging
- Certificate management
- Custom hostname configuration

## Development Notes

- **Sample Data**: Included for development when API is unavailable
- **Error Boundaries**: Widgets fail gracefully without breaking the page
- **Type Safety**: Full PHP type hints for better IDE support
- **Scalability**: Designed to handle large datasets efficiently

## Browser Compatibility

- Modern browsers supporting ES6+
- Chart.js for cross-browser chart rendering
- Responsive design for mobile/tablet viewing
