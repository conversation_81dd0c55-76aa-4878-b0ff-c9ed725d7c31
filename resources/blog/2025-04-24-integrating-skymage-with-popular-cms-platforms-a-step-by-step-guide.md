---
title: "Integrating Skymage with Popular CMS Platforms: A Step-by-Step Guide"
description: "Learn how to seamlessly integrate Skymage's powerful image optimization with WordPress, Drupal, and other popular CMS platforms for lightning-fast websites."
featured_image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71"
---

## Why CMS Integration Matters

Last week, a client came to me with a familiar problem: their content team loved their CMS for its ease of use, but their marketing team was frustrated with slow-loading images killing conversion rates. The developers were caught in the middle, looking for a solution that wouldn't require rebuilding their content workflow.

This is where Skymage shines. By integrating directly with popular CMS platforms, Skymage delivers optimized images without disrupting established content workflows. My client saw a 43% reduction in page load times within hours of implementation—without their content team even noticing a change in their workflow.

## WordPress Integration

WordPress powers over 40% of all websites, making it the most requested integration among my clients. Here's how Skymage transforms WordPress image performance:

### Method 1: Plugin Integration

For non-technical users, the plugin approach offers the easiest path:

1. Install the Skymage WordPress plugin from the WordPress plugin directory
2. Navigate to Settings → Skymage and enter your API key
3. Configure basic settings (automatic optimization, WebP/AVIF conversion, lazy loading)
4. Enable the CDN option to serve images through Skymage's global network

One of my e-commerce clients saw mobile conversion rates jump 17% after implementing this five-minute solution.

### Method 2: Direct Integration

For developers seeking more control, Skymage's direct integration offers advanced capabilities:

```php
// Add to functions.php
function optimize_with_skymage($url) {
    return 'https://demo.skymage/net/v1/' . parse_url($url, PHP_URL_HOST) . parse_url($url, PHP_URL_PATH);
}

// Filter image URLs
add_filter('wp_get_attachment_url', 'optimize_with_skymage');
add_filter('wp_calculate_image_srcset', function($sources) {
    foreach ($sources as &$source) {
        $source['url'] = optimize_with_skymage($source['url']);
    }
    return $sources;
});
```

This approach gives you precise control over which images are processed through Skymage and allows for custom query parameters for advanced transformations.

## Drupal Integration

Drupal's structured approach to media handling makes it an ideal candidate for Skymage integration:

### Using the Skymage Drupal Module

The dedicated Drupal module automates the entire process:

1. Install the Skymage module via Composer: `composer require skymage/drupal`
2. Enable the module in the Extensions section
3. Configure your API key and preferences at Configuration → Media → Skymage
4. Optionally enable per-content-type settings for granular control

### Custom Integration for Drupal 9+

For more advanced setups, implement Skymage at the theme level:

```php
function mytheme_preprocess_responsive_image(&$variables) {
  if (isset($variables['uri'])) {
    $original_uri = $variables['uri'];
    $host = \Drupal::request()->getHost();
    $path = file_url_transform_relative(file_create_url($original_uri));
    $variables['uri'] = 'https://demo.skymage/net/v1/' . $host . $path;
  }
}
```

## Shopify Integration

E-commerce imagery demands special attention, as product images directly impact conversion rates. Skymage's Shopify integration has been a game-changer for several of my retail clients:

### Using the Skymage Shopify App

1. Install the Skymage app from the Shopify App Store
2. Follow the setup wizard to connect your Skymage account
3. Configure product image optimization preferences
4. Enable automatic format conversion and responsive sizing

### Theme-Level Integration

For Shopify Plus merchants or those with custom themes, add this liquid code to your theme:

```liquid
{% assign img_url = product.featured_image | img_url: 'master' %}
{% assign optimized_url = "https://demo.skymage/net/v1/" | append: img_url | replace: "https://", "" %}
<img src="{{ optimized_url }}" alt="{{ product.featured_image.alt }}" />
```

## Headless CMS Integration

Modern composable architectures using headless CMS platforms benefit tremendously from Skymage integration:

### Contentful + Skymage

1. Set up a Contentful webhook to trigger when images are published
2. Configure the webhook to call Skymage's API for optimization
3. Update your frontend code to transform image URLs:

```javascript
// Transform Contentful image URLs to use Skymage
function getOptimizedImage(contentfulUrl, width = null) {
  const baseUrl = contentfulUrl.replace(/^https?:\/\//, '');
  let skymageUrl = `https://demo.skymage/net/v1/${baseUrl}`;

  if (width) {
    skymageUrl += `?w=${width}`;
  }

  return skymageUrl;
}
```

### Sanity + Skymage

Sanity's image pipeline pairs beautifully with Skymage:

```javascript
import imageUrlBuilder from '@sanity/image-url';

const builder = imageUrlBuilder(client);

function urlFor(source) {
  const sanityUrl = builder.image(source).url();
  return `https://demo.skymage/net/v1/${sanityUrl.replace(/^https?:\/\//, '')}`;
}
```

## Strapi Integration

Strapi's flexible approach to media management works seamlessly with Skymage:

1. Install the Skymage plugin: `npm install strapi-plugin-skymage`
2. Configure your API key in the plugin settings
3. Update your frontend to use optimized image URLs:

```javascript
// In your React component
function OptimizedImage({ image }) {
  const originalUrl = getStrapiMedia(image);
  const optimizedUrl = originalUrl.replace(
    /^https?:\/\/([^/]+)(\/.*)/,
    'https://demo.skymage/net/v1/$1$2'
  );

  return <img src={optimizedUrl} alt={image.alternativeText} />;
}
```

## Measuring the Impact

After integrating Skymage with your CMS, it's essential to measure the performance improvements:

1. Run before/after Lighthouse tests to quantify Core Web Vitals improvements
2. Track changes in key metrics like Largest Contentful Paint (LCP) and Cumulative Layout Shift (CLS)
3. Monitor bandwidth savings in your Skymage dashboard
4. Compare server load before and after offloading image processing

A media client recently saw their LCP times drop from 3.7s to 1.2s after integrating Skymage with their WordPress setup, significantly improving their search rankings.

## Common Integration Challenges

Based on dozens of integrations I've performed, here are solutions to common challenges:

### Mixed Content Warnings

If your site uses HTTPS, ensure all Skymage URLs also use HTTPS. The examples above all use secure connections.

### CORS Issues

If you're experiencing CORS issues with optimized images, configure your Skymage settings to include the proper Access-Control-Allow-Origin headers.

### Cache Invalidation

Set up webhooks from your CMS to trigger Skymage's purge API when content changes:

```php
// Example webhook handler
function purge_image_cache($image_url) {
    $api_key = 'your-skymage-api-key';
    $purge_url = 'https://api.skymage.dev/v1/purge';

    wp_remote_post($purge_url, [
        'headers' => ['Authorization' => 'Bearer ' . $api_key],
        'body' => ['url' => $image_url]
    ]);
}
```

## Next Steps with Skymage CMS Integration

Ready to transform your CMS images with Skymage? Here's how to get started:

1. [Sign up for Skymage](https://skymage.dev/register) and get your API key
2. Choose the integration method that fits your technical comfort level
3. Start with a subset of images to test the impact
4. Gradually roll out to your entire image library
5. Set up monitoring to track performance improvements

By seamlessly integrating Skymage with your CMS, you'll deliver lightning-fast, perfectly optimized images without disrupting your content workflow. Your users get a better experience, your SEO improves, and your content team keeps their familiar tools.

Want personalized help with your CMS integration? [Contact the Skymage team](https://skymage.dev/contact) for expert assistance.