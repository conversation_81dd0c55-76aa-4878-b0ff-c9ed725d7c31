<x-layouts.marketing
    title="Image Optimization Blog | Skymage CDN"
    description="Stay up-to-date with the latest articles and tutorials about image optimization, web performance, and CDN strategies to improve your website's speed and user experience."
    ogImage="https://skymage.daudau.cc/og.png">

    <div class="container mx-auto py-12 px-4">
        <header class="max-w-3xl mx-auto mb-12 text-center">
            <h1 class="text-4xl font-bold text-primary-600 mb-4">Image Optimization Blog</h1>
            <p class="text-base text-gray-600">Expert articles and insights about web performance, image optimization strategies, and CDN technologies</p>
        </header>

        @if(count($posts) > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" itemscope itemtype="https://schema.org/Blog">
                @foreach($posts as $post)
                    <article itemscope itemtype="https://schema.org/BlogPosting" class="h-full">
                        <meta itemprop="datePublished" content="{{ $post['date']->toIso8601String() }}">
                        <meta itemprop="author" content="Skymage Team">
                        <meta itemprop="publisher" content="Skymage">
                        <link itemprop="url" href="{{ route('blog.show', $post['slug']) }}">
                        <x-blog-post-card :post="$post" />
                    </article>
                @endforeach
            </div>
        @else
            <div class="text-center py-12">
                <p class="text-gray-500 text-lg">{{ __("No blog posts found.") }}</p>
            </div>
        @endif
    </div>
</x-layouts.marketing>
