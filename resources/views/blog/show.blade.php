<x-layouts.marketing
    :title="$title"
    :description="$description"
    :ogImage="skymagecdn($featured_image, ['w' => 1200, 'h' => 630, 'fit' => 'crop'])"
    :ogType="'article'"
    :ogUrl="url()->current()"
    :publishedTime="$date->toIso8601String()"
    :authorName="'Skymage Team'">

    <div class="container mx-auto max-w-3xl px-4 py-12">
        <article itemscope itemtype="https://schema.org/BlogPosting">
            <meta itemprop="publisher" content="Skymage">
            <meta itemprop="datePublished" content="{{ $date->toIso8601String() }}">
            <meta itemprop="dateModified" content="{{ $date->toIso8601String() }}">
            <meta itemprop="author" content="Skymage Team">
            <meta itemprop="mainEntityOfPage" content="{{ url()->current() }}">

            <div class="mb-8">
                <div class="mb-4">
                    <a href="{{ route('blog.index') }}" class="text-primary-600 hover:text-primary-700 inline-flex items-center">
                        <x-heroicon-o-arrow-left class="w-4 h-4 mr-1" />
                        {{ __('Back to Blog') }}
                    </a>
                </div>

                <div class="relative aspect-[16/7] mb-8 rounded-xl overflow-hidden shadow-lg">
                    <img
                        src="{{ skymagecdn($featured_image, ['w' => 850, 'h' => 322, 'fit' => 'cover', 'q' => 85]) }}"
                        alt="{{ $title }}"
                        width="850"
                        height="322"
                        itemprop="image"
                        class="object-cover w-full h-full"
                    />
                </div>

                <h1 class="text-3xl md:text-4xl font-bold mb-4" itemprop="headline">{{ $title }}</h1>

                @if(!empty($description))
                    <p class="text-gray-600 mb-6" itemprop="description">{{ $description }}</p>
                @endif

                <div class="text-sm text-gray-500 mb-6 flex items-center">
                    <x-heroicon-o-calendar class="w-4 h-4 mr-1" aria-hidden="true" />
                    <time datetime="{{ $date->format('Y-m-d') }}" itemprop="datePublished">{{ $date->format('F j, Y') }}</time>
                </div>
            </div>

            <div class="prose prose-lg prose-primary max-w-none mb-12" itemprop="articleBody">
                {!! \Illuminate\Support\Str::markdown($content) !!}
            </div>

            <div class="border-t border-gray-200 pt-8">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                        <h3 class="text-lg font-semibold mb-2">{{ __('Share this article:') }}</h3>
                        <div class="flex space-x-4">
                            <a href="https://twitter.com/intent/tweet?text={{ urlencode($title) }}&url={{ urlencode(url()->current()) }}"
                                target="_blank"
                                rel="noopener"
                                aria-label="Share on Twitter"
                                class="text-gray-500 hover:text-primary-600">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                                </svg>
                            </a>
                            <a href="https://www.linkedin.com/shareArticle?mini=true&url={{ urlencode(url()->current()) }}&title={{ urlencode($title) }}"
                                target="_blank"
                                rel="noopener"
                                aria-label="Share on LinkedIn"
                                class="text-gray-500 hover:text-primary-600">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
                                </svg>
                            </a>
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(url()->current()) }}"
                                target="_blank"
                                rel="noopener"
                                aria-label="Share on Facebook"
                                class="text-gray-500 hover:text-primary-600">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd"></path>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <div>
                        <a href="{{ route('blog.index') }}" class="inline-flex items-center text-primary-600 hover:text-primary-700">
                            {{ __('View more articles') }}
                            <x-heroicon-o-arrow-right class="w-4 h-4 ml-1" aria-hidden="true" />
                        </a>
                    </div>
                </div>
            </div>
        </article>

        @if(isset($relatedPosts) && count($relatedPosts) > 0)
            <div class="mt-16">
                <h2 class="text-2xl font-bold mb-8">{{ __('Related Articles') }}</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    @foreach($relatedPosts as $relatedPost)
                        <x-blog-post-card :post="$relatedPost" />
                    @endforeach
                </div>
            </div>
        @endif
    </div>
</x-layouts.marketing>