@php
    use App\Models\PullZone;
    /* @var PullZone $pullZone */
@endphp
<x-filament-panels::page>
    @if (!$pullZone)
        <x-filament::section class="items-center justify-center">
            <div class="flex items-center justify-center text-sm">
                @lang("You CDN will be ready soon. Please contact us if it's taking too long.")
            </div>
        </x-filament::section>
    @else
        @if($pullZone->isProvisioning())
            <x-filament::section class="items-center justify-center animate-pulse">
                <div class="mt-6 text-sm text-gray-500 text-center">
                    @lang('Your CDN is provisioning and will be ready in minutes.')
                </div>
            </x-filament::section>
        @endif

        {{ $this->pullZoneInfolist }}
    @endif

</x-filament-panels::page>
