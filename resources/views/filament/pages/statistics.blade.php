<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Date Range Filter -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            {{ $this->form }}
        </div>

        <!-- Statistics Overview Widget -->
        <div class="grid grid-cols-1 gap-6">
            <x-loading-skeleton event="date-filters-updated" timeout="1000">
                @livewire(\App\Filament\Widgets\StatisticsOverview::class)
            </x-loading-skeleton>
        </div>

        <!-- Chart Widgets -->
        <div class="grid grid-cols-1 gap-6">
            <x-loading-skeleton event="date-filters-updated" timeout="1500">
                @livewire(\App\Filament\Widgets\BandwidthChart::class)
            </x-loading-skeleton>

            <x-loading-skeleton loading="false" event="date-filters-updated" timeout="1500">
                @livewire(\App\Filament\Widgets\RequestsChart::class)
            </x-loading-skeleton>

            <x-loading-skeleton loading="false" event="date-filters-updated" timeout="1500">
                @livewire(\App\Filament\Widgets\OriginTrafficChart::class)
            </x-loading-skeleton>
        </div>
    </div>
</x-filament-panels::page>
