<x-filament-panels::page>
    @php
        /**
         * @var \App\Models\User $user
         */
        $user = auth()->user();
    @endphp
    @if ($user->onGenericTrial())
        <div class="bg-yellow-50 text-primary-700 rounded-md p-4 mb-4 text-sm">
            {{ __('You are currently on a trial plan. You can upgrade or cancel your trial at any time.') }}
            {{ __('Your trial ends at: :date', ['date' => $user->trialEndsAt()->format('Y-m-d')]) }}
        </div>
    @endif

    @if ($user->subscribed())
        <div class="bg-primary-50 text-primary-600 rounded-md p-4 mb-4 text-sm">
            <p>{{ __('You are currently subscribed to the plan') }}: <strong>{{ $user->plan()?->name  }}</strong></p>
            @if ($user->subscription()->active())
                <p>{{ __('Your next payment is due on') }}: <strong>{{ $user->subscription()->renews_at->toDateString() }}</strong></p>
            @endif
            @if ($user->subscription()->cancelled())
                <p>{{ __('Your subscription will end on') }}: <strong>{{ $user->subscription()->ends_at->toDateString() }}</strong></p>
            @endif
        </div>
    @endif

    <x-pricing :show-heading="false" />
</x-filament-panels::page>
