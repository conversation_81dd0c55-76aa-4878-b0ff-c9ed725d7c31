@props([
    'showHeading' => true,
    'hideCheckoutButtons' => false,
    'showTryFreeButton' => false,
    'showTryFreeOnPlans' => false,
])

<x-section {{ $attributes }} class="pt-0">
    @if ($showHeading)
        <!-- Launch Promotion Badge -->
        <div class="text-center mb-6">
            <x-ui.promotion-badge variant="primary" size="lg" class="shadow-lg border-2">
                {{ __('🚀 Launch Special: 30% Off Lifetime with Code SKYLAUNCH') }}
            </x-ui.promotion-badge>
        </div>

        <x-heading class="text-center" id="pricing">
            <span class="block">Start Optimizing Today</span>
        </x-heading>
        <p class="mb-8 mt-2 text-center text-gray-600 max-w-3xl mx-auto text-lg">
            {{ __('Choose the plan that fits your needs. All plans include our 30-day money-back guarantee.') }}
        </p>
    @endif

    <div x-data="{isYearly: false}">
        <!-- the plan switcher -->
        <div class="flex flex-col items-center">
            <div class="flex items-center gap-2 mb-2">
                <div class="w-96 flex justify-center mx-auto border rounded-full">
                    <div class="relative flex w-full p-1 bg-white rounded-full">
                        <div class="absolute inset-0 m-1 pointer-events-none" aria-hidden="true">
                            <span
                                class="absolute inset-0 w-1/2 bg-primary-500 rounded-full shadow-sm shadow-indigo-950/10 transform transition-transform duration-150 ease-in-out"
                                :class="isYearly ? 'translate-x-full' : 'translate-x-0'"></span>
                        </div>
                        <button
                            class="relative flex-1 text-sm font-medium h-8 rounded-full focus-visible:outline-none focus-visible:ring focus-visible:ring-primary-300 transition-colors duration-150 ease-in-out"
                            :class="isYearly ? 'text-slate-500 ' : 'text-white'" @click="isYearly = false"
                            :aria-pressed="isYearly">
                            Monthly
                        </button>
                        <button
                            class="relative flex-1 text-sm font-medium h-8 rounded-full focus-visible:outline-none focus-visible:ring focus-visible:ring-primary-300 transition-colors duration-150 ease-in-out"
                            :class="isYearly ? 'text-white' : 'text-slate-500'" @click="isYearly = true"
                            :aria-pressed="isYearly">
                            Yearly - save 2 months 🔥
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-12 container mx-auto flex flex-col md:flex-row items-start justify-center gap-6">
            @foreach(\App\Billing\Billing::activePlans() as $index => $plan)
                <div
                    style="display: none"
                    x-show="isYearly === {{ $plan->isYearly() ? 'true' : 'false' }}"
                    @class([
                        "w-full md:w-96 bg-white rounded-xl border shadow-sm transition-all duration-300 hover:shadow-lg relative",
                    ])>

                    @if($plan->isPopular())
                        <div class="absolute -top-4 left-0 right-0 mx-auto w-max px-4 py-1.5 bg-gradient-to-r from-primary-600 to-primary-500 text-white text-xs font-bold rounded-full shadow-sm">
                            <div class="flex items-center gap-1">
                                <x-heroicon-s-star class="h-3.5 w-3.5" />
                                @lang('Most popular')
                            </div>
                        </div>
                    @endif

                    <div @class([
                        "p-6 text-center",
                        "rounded-t-xl bg-gradient-to-b from-primary-50 to-white" => $plan->isPopular()
                    ])>
                        <p class="text-lg lg:text-xl leading-6 font-semibold {{ $plan->isPopular() ? 'text-primary-700' : '' }}">{{ $plan->name }}</p>
                        <p class="mt-6">
                            <span class="text-4xl font-bold text-gray-900">${{ $plan->getPrice() }}</span>
                            <span class="font-medium text-gray-500 text-sm">/{{ $plan->isYearly() ? 'year' : 'month' }}</span>
                        </p>
                        <p class="mt-4 text-sm h-6 text-gray-600">{{ $plan->shortDescription }}</p>

                        @if(!$hideCheckoutButtons)
                            <a
                                href="{{ route('checkout.create', ['planId' => $plan->id]) }}"
                                @class([
                                    "mt-8 block w-full border border-transparent rounded-lg py-3 text-sm font-semibold text-white text-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg",
                                    "bg-primary-500 hover:bg-primary-600" => $plan->isPopular(),
                                    "bg-gray-900 hover:bg-gray-800" => !$plan->isPopular()
                                ])>
                                {{ $plan->isPopular() ? 'Get 30% Off Lifetime' : 'Start Free Trial' }}
                            </a>
                        @elseif($showTryFreeOnPlans)
                            <a
                                href="/register"
                                @class([
                                    "mt-8 block w-full border border-transparent rounded-lg py-3 text-sm font-semibold text-white text-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg",
                                    "bg-primary-500 hover:bg-primary-600" => $plan->isPopular(),
                                    "bg-gray-900 hover:bg-gray-800" => !$plan->isPopular()
                                ])>
                                {{ $plan->isPopular() ? 'Start Free Trial' : 'Try Free' }}
                            </a>
                            <p class="mt-2 text-xs text-gray-500 text-center">{{ __('No credit card required') }}</p>
                        @endif
                    </div>
                    <div class="pb-8 px-6 {{ $plan->isPopular() ? 'bg-gradient-to-b from-white to-primary-50' : '' }}">
                        <p class="text-sm font-semibold tracking-wide uppercase text-gray-700">What's included</p>
                        <ul class="mt-6 space-y-4 text-sm">
                            @foreach($plan->features as $feature)
                                <li class="flex items-start">
                                    <x-heroicon-s-check-circle class="h-5 w-5 text-primary-500 shrink-0 mr-2" />
                                    <span class="text-gray-600">{{ $feature }}</span>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            @endforeach
        </div>

        @if($showTryFreeButton)
            <div class="text-center">
                <a href="/register" class="inline-block bg-primary hover:bg-primary-600 text-white font-medium px-8 py-3 rounded-lg transition-colors">
                    {{ __('Try Free for 14 Days') }}
                </a>
                <p class="mt-2 text-sm text-gray-600">
                    {{ __('No credit card required') }}
                </p>
            </div>
        @endif

        @unless($hideCheckoutButtons)
            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600">Need a custom plan? <a href="/contact" class="text-primary-500 hover:text-primary-600 font-medium">Contact us</a></p>
            </div>
        @endunless
    </div>
</x-section>
