<div class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <x-ui.section-header
            tagline="Performance Metrics"
            title="Improve Your Image Performance Scores"
            description="Skymage helps optimize the image-related aspects of your site's performance, which can significantly contribute to better Core Web Vitals and Lighthouse scores."
        />

        <div class="mt-10">
            <!-- Direct Before/After Comparison -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Lighthouse Score Card - Before -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
                    <div class="p-4 bg-gray-50 border-b border-gray-100 flex justify-between items-center">
                        <h3 class="font-semibold text-lg">Before Skymage</h3>
                        <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Needs Improvement</span>
                    </div>
                    <div class="p-6 space-y-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-3">
                                    <span class="text-red-600 font-bold">58</span>
                                </div>
                                <span class="font-medium">Performance</span>
                            </div>
                            <div class="w-full max-w-xs bg-gray-200 rounded-full h-2.5 ml-4">
                                <div class="bg-red-500 h-2.5 rounded-full" style="width: 58%"></div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                                    <span class="text-yellow-600 font-bold">85</span>
                                </div>
                                <span class="font-medium">Image Optimization</span>
                            </div>
                            <div class="w-full max-w-xs bg-gray-200 rounded-full h-2.5 ml-4">
                                <div class="bg-yellow-500 h-2.5 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-3">
                                    <span class="text-red-600 font-bold">62</span>
                                </div>
                                <span class="font-medium">Properly Sized Images</span>
                            </div>
                            <div class="w-full max-w-xs bg-gray-200 rounded-full h-2.5 ml-4">
                                <div class="bg-red-500 h-2.5 rounded-full" style="width: 62%"></div>
                            </div>
                        </div>

                        <div>
                            <div class="flex justify-between mb-2">
                                <span class="font-medium">Image Load Time</span>
                                <span class="text-red-600 font-medium">2.8s</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-red-500 h-2.5 rounded-full" style="width: 70%"></div>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Poor: Images are taking too long to load</p>
                        </div>
                    </div>
                </div>

                <!-- Lighthouse Score Card - After -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-primary-100 relative">
                    <!-- Arrow connecting the two cards on desktop -->
                    <div class="absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block">
                        <div class="bg-primary text-white rounded-full p-2 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </div>
                    </div>

                    <!-- Arrow connecting the two cards on mobile -->
                    <div class="absolute top-0 left-1/2 transform -translate-y-full -translate-x-1/2 lg:hidden">
                        <div class="bg-primary text-white rounded-full p-2 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                        </div>
                    </div>

                    <div class="p-4 bg-primary-50 border-b border-primary-100 flex justify-between items-center">
                        <h3 class="font-semibold text-lg">After Skymage</h3>
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Optimized</span>
                    </div>
                    <div class="p-6 space-y-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                    <span class="text-green-600 font-bold">78</span>
                                </div>
                                <span class="font-medium">Performance</span>
                            </div>
                            <div class="w-full max-w-xs bg-gray-200 rounded-full h-2.5 ml-4">
                                <div class="bg-green-500 h-2.5 rounded-full" style="width: 78%"></div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                    <span class="text-green-600 font-bold">96</span>
                                </div>
                                <span class="font-medium">Image Optimization</span>
                            </div>
                            <div class="w-full max-w-xs bg-gray-200 rounded-full h-2.5 ml-4">
                                <div class="bg-green-500 h-2.5 rounded-full" style="width: 96%"></div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                    <span class="text-green-600 font-bold">94</span>
                                </div>
                                <span class="font-medium">Properly Sized Images</span>
                            </div>
                            <div class="w-full max-w-xs bg-gray-200 rounded-full h-2.5 ml-4">
                                <div class="bg-green-500 h-2.5 rounded-full" style="width: 94%"></div>
                            </div>
                        </div>

                        <div>
                            <div class="flex justify-between mb-2">
                                <span class="font-medium">Image Load Time</span>
                                <span class="text-green-600 font-medium">0.8s</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-green-500 h-2.5 rounded-full" style="width: 90%"></div>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Good: Images now load significantly faster</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Core Web Vitals Impact -->
            <div class="mt-12 bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
                <div class="p-4 bg-gray-50 border-b border-gray-100">
                    <h3 class="font-semibold text-lg">Impact on Core Web Vitals</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="font-medium">Largest Contentful Paint</h4>
                                <div class="flex items-center">
                                    <span class="text-red-600 font-medium line-through mr-2">4.2s</span>
                                    <span class="text-orange-600 font-medium">3.1s</span>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                                    <div class="bg-red-500 h-2.5 rounded-full" style="width: 75%"></div>
                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 ml-2">
                                    <div class="bg-orange-500 h-2.5 rounded-full" style="width: 65%"></div>
                                </div>
                            </div>
                            <p class="mt-2 text-xs text-gray-500">Improved but still needs work: Other factors affect LCP</p>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="font-medium">Image Load Time</h4>
                                <div class="flex items-center">
                                    <span class="text-red-600 font-medium line-through mr-2">2.8s</span>
                                    <span class="text-green-600 font-medium">0.8s</span>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                                    <div class="bg-red-500 h-2.5 rounded-full" style="width: 70%"></div>
                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 ml-2">
                                    <div class="bg-green-500 h-2.5 rounded-full" style="width: 90%"></div>
                                </div>
                            </div>
                            <p class="mt-2 text-xs text-gray-500">Significant improvement: Images load much faster</p>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="font-medium">Cumulative Layout Shift</h4>
                                <div class="flex items-center">
                                    <span class="text-red-600 font-medium line-through mr-2">0.28</span>
                                    <span class="text-orange-600 font-medium">0.15</span>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                                    <div class="bg-red-500 h-2.5 rounded-full" style="width: 80%"></div>
                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 ml-2">
                                    <div class="bg-orange-500 h-2.5 rounded-full" style="width: 65%"></div>
                                </div>
                            </div>
                            <p class="mt-2 text-xs text-gray-500">Improved: Properly sized images help reduce layout shift</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Improvement Metrics -->
            <div class="mt-10 bg-gray-50 rounded-xl p-6 border border-gray-200">
                <h3 class="text-xl font-semibold text-center mb-6">Key Image Improvements with Skymage</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white p-4 rounded-lg shadow-sm text-center">
                        <div class="text-3xl font-bold text-primary mb-2">-71%</div>
                        <div class="text-sm text-gray-600">Image File Size Reduction</div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm text-center">
                        <div class="text-3xl font-bold text-primary mb-2">-72%</div>
                        <div class="text-sm text-gray-600">Image Load Time Improvement</div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm text-center">
                        <div class="text-3xl font-bold text-primary mb-2">+15</div>
                        <div class="text-sm text-gray-600">Lighthouse Image Score Increase</div>
                    </div>
                </div>
            </div>

            <!-- Explanation -->
            <div class="mt-8 text-center text-gray-600 max-w-3xl mx-auto">
                <p>While image optimization alone won't fix all performance issues, it's a critical component of web performance. Skymage helps you optimize the image-specific aspects of your site, which can contribute significantly to better overall performance metrics and user experience.</p>
            </div>
        </div>
    </div>
</div>
