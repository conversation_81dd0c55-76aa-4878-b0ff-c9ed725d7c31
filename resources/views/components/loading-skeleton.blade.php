@props([
    'loading' => 'false',
    'event' => 'date-filters-updated',
    'timeout' => 1500,
])

<div class="relative">
    {{ $slot }}

    <div
        x-data="{ loading: {{ $loading }} }"
        x-on:{{ $event }}.window="loading = true; setTimeout(() => loading = false, {{ $timeout }})"
        x-show="loading"
        x-transition.opacity.duration.300ms
        class="absolute inset-0 bg-gray-500/20 backdrop-blur-xs bg-opacity-20 rounded-lg flex items-center justify-center z-10"
        style="display: none;"
    >
        <div>
            <svg class="animate-spin h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="sr-only text-sm font-medium text-gray-700">{{ __('Loading...') }}</span>
        </div>
    </div>
</div>
