@props(['title', 'description', 'image'])

<div class="bg-gradient-to-r from-sky-50 to-gray-50 py-16">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row items-center gap-12">
            <div class="lg:w-1/2">
                <a href="{{ route('use-cases') }}" class="text-primary hover:text-primary-700 inline-flex items-center mb-4">
                    <x-heroicon-o-arrow-left class="mr-1 w-4 h-4" />
                    {{ __('Back to all use cases') }}
                </a>
                <h1 class="text-4xl lg:text-5xl font-bold mb-6">{{ $title }}</h1>
                <p class="text-xl text-gray-700 mb-8">{{ $description }}</p>
                <div>
                    <a href="/register" class="btn bg-primary hover:bg-primary-600 text-white px-6 py-3 rounded-lg inline-flex items-center justify-center">
                        {{ __('Start Free Trial') }}
                    </a>
                </div>
            </div>
            <div class="lg:w-1/2">
                <img src="{{ $image }}" alt="{{ $title }}" class="rounded-lg shadow-lg w-full object-cover h-96">
            </div>
        </div>
    </div>
</div>
