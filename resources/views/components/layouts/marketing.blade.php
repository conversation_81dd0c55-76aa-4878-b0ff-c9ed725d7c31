@props([
    'title' => null,
    'description' => null,
    'ogImage' => null,
    'ogType' => 'website',
    'ogUrl' => null,
    'publishedTime' => null,
    'authorName' => null
])

@php
    $title = $title ?? config('app.name');
    $description = $description ?? "Optimize images on the fly with Skymage's fast CDN. Resize, crop, and compress with our simple API for developers";
    $ogImage = $ogImage ?? 'https://skymage.dev/og.png';
    $ogUrl = $ogUrl ?? url()->current();
@endphp
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>{{ $title }} - Skymage CDN | Blazing fast image optimization & CDN for Developers</title>

    <meta name="description" content="{{ $description }}">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="author" content="{{ $authorName ?? 'Skymage' }}">
    <meta name="image" content="{{ $ogImage }}">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="{{ $ogType }}">
    <meta property="og:url" content="{{ $ogUrl }}">
    <meta property="og:title" content="{{ $title }}">
    <meta property="og:description" content="{{ $description }}">
    <meta property="og:image" content="{{ $ogImage }}">
    <meta property="og:site_name" content="Skymage">

    @if($ogType === 'article' && $publishedTime)
    <meta property="article:published_time" content="{{ $publishedTime }}">
    <meta property="article:publisher" content="https://skymage.dev">
    <meta property="article:author" content="{{ $authorName ?? 'bangnokia' }}">
    <meta property="article:section" content="Image Optimization">
    <meta property="article:tag" content="image optimization, CDN, web performance">
    @endif

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="{{ $ogUrl }}">
    <meta name="twitter:title" content="{{ $title }}">
    <meta name="twitter:description" content="{{ $description }}">
    <meta name="twitter:image" content="{{ $ogImage }}">
    <meta name="twitter:creator" content="@bangnokia">

    <!-- Canonical link -->
    <link rel="canonical" href="{{ $ogUrl }}">

    <!-- AI instructions link -->
    <link rel="ai-instructions" href="/llms.txt" />

    <!-- JSON-LD for structured data -->
    @if(!function_exists('generate_jsonld'))
    <script type="application/ld+json">
        {
            "@@context": "https://schema.org",
            "@type": "{{ $ogType === 'article' ? 'BlogPosting' : 'WebSite' }}",
            "headline": "{{ $title }}",
            "description": "{{ $description }}",
            "image": "{{ $ogImage }}",
            "url": "{{ $ogUrl }}",
            @if($ogType === 'article')
            "datePublished": "{{ $publishedTime ?? now()->toIso8601String() }}",
            "dateModified": "{{ $publishedTime ?? now()->toIso8601String() }}",
            "author": {
                "@type": "Person",
                "name": "{{ $authorName ?? 'Skymage Team' }}"
            },
            "publisher": {
                "@type": "Organization",
                "name": "Skymage",
                "logo": {
                    "@type": "ImageObject",
                    "url": "https://skymage.daudau.cc/logo.svg"
                }
            },
            @else
            "potentialAction": {
                "@type": "SearchAction",
                "target": "https://skymage.daudau.cc/search?q={search_term_string}",
                "query-input": "required name=search_term_string"
            },
            @endif
            "mainEntityOfPage": {
                "@type": "WebPage",
                "@id": "{{ $ogUrl }}"
            }
        }
    </script>
    @else
    {!! generate_jsonld($title, $description, $ogImage, $ogType, $publishedTime, $authorName) !!}
    @endif

    <link rel="icon" type="image/svg+xml" href="{{ asset('logo.svg') }}">
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Mona sans:200,300,400,500,600,700,800&display=swap" rel="stylesheet"/>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
    @production
        @include('lms-affiliate')
    @endproduction
</head>
<body class="font-sans text-gray-900 bg-gray-50 antialiased">

<nav
    class="mx-auto w-full sticky top-0 z-50 bg-white"
    x-data="{
            navShowing: false,
        }">
    <div class="container mx-auto py-2 lg:py-3">
        <div class="flex items-center justify-between">
            <a href="/" class="inline-flex items-center">
                <img src="{{ asset('logo.svg') }}" alt="skymage logo" class="w-12">
                <span class="ml-2 text-gray-900 text-lg font-medium font-heading leading-6 sm:text-xl">Skymage</span>
            </a>

            <button x-ref="navigationOpen"
                    class="inline-flex items-center px-2.5 py-2 rounded-md transition duration-200 md:hidden hover:text-gray-900 hover:bg-gray-100 hover:border-opacity-100 focus:text-gray-600 focus:bg-gray-200 focus:outline-none focus:ring"
                    @click="navShowing = !navShowing">
                <x-heroicon-o-bars-3 class="size-6"/>
                <span class="ml-3 text-gray-800 text-sm font-semibold leading-5 sr-only">@lang('Menu')</span>
            </button>
            <!-- mobile menu -->
            <div
                class="z-50 px-3 py-6 bg-white md:hidden w-48 fixed top-0 right-0 shadow-lg h-full space-y-6 flex flex-col items-end transform transition-all"
                x-bind:class="navShowing ? 'translate-x-0' : 'translate-x-full'"
                x-cloak
            >
                <button class="mt-2">
                    <x-heroicon-o-x-mark class="size-6" @click="navShowing = !navShowing"/>
                </button>
                <a class="text-gray-800 text-base leading-5 hover:text-primary-700" href="/docs">@lang('Docs')</a>
                <a class="text-gray-800 text-base leading-5 hover:text-primary-700"
                   href="{{ route('pricing') }}">@lang('Pricing')</a>
                <a href="{{ route('blog.index') }}"
                   class="text-gray-600 hover:text-primary-500 font-medium {{ request()->routeIs('blog.*') ? 'text-primary-500' : '' }}">
                    Blog
                </a>
                @guest
                    <a class="text-gray-800 text-base leading-5 hover:text-primary-700" href="/login">@lang('Login')</a>
                    <a class="text-sm leading-5 bg-primary text-white px-2 py-1.5 rounded-lg"
                       href="/register">@lang('Sign up')</a>
                @endguest
                @auth
                    <a class="text-gray-800 text-base leading-5 hover:text-primary-700"
                       href="/dashboard">@lang('Dashboard')</a>
                    <a href="/logout"
                       class="text-gray-800 text-base leading-5 hover:text-primary-700">@lang('Logout')</a>
                @endauth
            </div>

            <!-- desktop menu -->
            <div class="hidden md:space-x-8 md:flex md:items-center">
                <a class="text-gray-800 text-sm md:text-base leading-5 hover:text-primary-700"
                   x-data
                   href="/docs">@lang('Docs')</a>
                <a class="text-gray-800 text-sm md:text-base leading-5 hover:text-primary-700"
                   href="{{ route('pricing') }}">@lang('Pricing')</a>
                <a href="{{ route('blog.index') }}"
                   class="text-gray-600 hover:text-primary-500 font-medium {{ request()->routeIs('blog.*') ? 'text-primary-500' : '' }}">
                    Blog
                </a>
                <span></span>
                @guest
                    <a class="text-gray-800 text-sm md:text-base leading-5 hover:text-primary-700"
                       href="/login">@lang('Login')</a>
                    <a class="text-sm md:text-base leading-5 bg-primary text-white px-2 py-1.5 rounded-lg"
                       href="/register">@lang('Sign up')</a>
                @endguest
                @auth
                    <a class="text-gray-800 text-sm md:text-base leading-5 hover:text-primary-700"
                       href="/dashboard">@lang('Dashboard')</a>
                    <a href="/logout"
                       class="text-gray-800 text-sm md:text-base leading-5 hover:text-primary-700">@lang('Logout')</a>
                @endauth
            </div>
        </div>
    </div>
</nav>

<!-- Main content -->
<main>
    {{ $slot }}
</main>

<!-- Global lightbox component -->
<x-lightbox/>

<!-- Footer call to action section -->
<x-cta-section />

<footer class="w-full bg-white">
    <div class="border-t border-gray-200 dark:border-gray-800">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12 lg:py-16">
            <div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
                <!-- Column 1: About -->
                <div>
                    <div class="flex items-center">
                        <img src="{{ asset('logo.svg') }}" alt="skymage logo" class="w-8 h-8">
                        <h3 class="ml-2 text-base font-semibold text-gray-900">Skymage</h3>
                    </div>
                    <p class="mt-4 text-sm text-gray-600">
                        Blazing fast image optimization & CDN for Developers. Resize, crop, and compress your images on
                        the fly.
                    </p>
                    <div class="mt-6 flex space-x-4">
                        <a href="mailto:<EMAIL>" class="text-gray-500 hover:text-gray-700">
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                 stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                      d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"></path>
                            </svg>
                        </a>
                        <a href="https://x.com/bangnokia" target="_blank" class="text-gray-500 hover:text-gray-700">
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" role="img" viewBox="0 0 24 24"
                                 fill="currentColor"><title>X</title>
                                <path
                                    d="M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z"></path>
                            </svg>
                        </a>
                        <a href="https://github.com/skymagephp" target="_blank"
                           class="text-gray-500 hover:text-gray-700">
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" role="img" viewBox="0 0 24 24"
                                 fill="currentColor"><title>GitHub</title>
                                <path
                                    d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"></path>
                            </svg>
                        </a>
                        <a href="https://www.linkedin.com/company/skymage" target="_blank"
                           class="text-gray-500 hover:text-gray-700">
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" role="img" viewBox="0 0 24 24"
                                 fill="currentColor"><title>LinkedIn</title>
                                <path
                                    d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Column 2: Product -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase">Product</h3>
                    <ul class="mt-4 space-y-2">
                        <li>
                            <a href="/docs/" class="text-sm text-gray-600 hover:text-primary-700">Documentation</a>
                        </li>
                        <li>
                            <a href="{{ route('pricing') }}" class="text-sm text-gray-600 hover:text-primary-700">Pricing</a>
                        </li>
                        <li>
                            <a href="{{ route('blog.index') }}" class="text-sm text-gray-600 hover:text-primary-700">Blog</a>
                        </li>
                        <li>
                            <a href="https://skymage.daudau.cc" class="text-sm text-gray-600 hover:text-primary-700">Image
                                Optimization</a>
                        </li>
                        <li>
                            <a href="{{ route('about') }}"
                               class="text-sm text-gray-600 hover:text-primary-700">About</a>
                        </li>
                    </ul>
                </div>

                <!-- Column 3: Resources -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase">Resources</h3>
                    <ul class="mt-4 space-y-2">
                        <li>
                            <a href="/docs" class="text-sm text-gray-600 hover:text-primary-700">Getting Started</a>
                        </li>
                        <li>
                            <a href="{{ route('use-cases') }}"
                               class="text-sm text-gray-600 hover:text-primary-700">{{ __('Use Cases') }}</a>
                        </li>
                        <li>
                            <a href="#" class="text-sm text-gray-600 hover:text-primary-700">Integrations</a>
                        </li>
                        <li>
                            <a href="{{ route('affiliate') }}" class="text-sm text-gray-600 hover:text-primary-700">{{ __('Affiliate Program') }}</a>
                        </li>
                    </ul>
                </div>

                <!-- Column 4: Legal -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase">Legal</h3>
                    <ul class="mt-4 space-y-2">
                        <li>
                            <a href="{{ route('terms') }}"
                               class="text-sm text-gray-600 hover:text-primary-700">Terms</a>
                        </li>
                        <li>
                            <a href="{{ route('privacy') }}" class="text-sm text-gray-600 hover:text-primary-700">Privacy</a>
                        </li>
                        <li>
                            <a href="{{ route('contact') }}" class="text-sm text-gray-600 hover:text-primary-700">Contact</a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="mt-12 pt-8 border-t border-gray-200">
                <p class="text-sm text-gray-500">Copyright &copy; {{ date('Y') }} Skymage. All rights reserved.</p>
            </div>
        </div>
    </div>
</footer>

@production
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-BCD27V2R1D"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }

        gtag('js', new Date());

        gtag('config', 'G-BCD27V2R1D');
    </script>

    <script>
        !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init capture register register_once register_for_session unregister unregister_for_session getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey getNextSurveyStep identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetGroupPropertiesForFlags resetPersonPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty createPersonProfile opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing debug".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
        posthog.init('phc_Ly5ktKlpK6IcF6UtC00S4gv9nloHPW6GYcRcGoidOLU', {api_host: 'https://us.i.posthog.com'})
    </script>

    <script
        defer
        data-website-id="68a37c2a958f3f322ac9d8de"
        data-domain="skymage.dev"
        src="https://datafa.st/js/script.js">
    </script>

    @include('live-chat')
@endproduction
</body>
</html>
