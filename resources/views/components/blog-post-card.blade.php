@props(['post'])

<div {{ $attributes->class(['flex flex-col h-full border border-gray-100 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition']) }} itemprop="mainEntityOfPage">
    @if(!empty($post['featured_image']))
        <a href="{{ route('blog.show', $post['slug']) }}" class="block" aria-label="Featured image for {{ $post['title'] }}">
            <div class="aspect-[16/9] overflow-hidden">
                <img src="{{ skymagecdn($post['featured_image'], ['w' => 600, 'h' => 338, 'fit' => 'cover', 'q' => 85]) }}"
                     alt="{{ $post['title'] }}"
                     loading="lazy"
                     width="600"
                     height="338"
                     itemprop="image"
                     class="w-full h-full object-cover">
            </div>
        </a>
    @endif
    <div class="flex flex-col flex-grow p-5">
        <div class="text-sm text-gray-500 mb-2">
            <time datetime="{{ $post['date']->toIso8601String() }}">{{ $post['date']->format('F j, Y') }}</time>
        </div>
        <h2 class="text-xl font-bold mb-3" itemprop="headline">
            <a href="{{ route('blog.show', $post['slug']) }}" class="text-gray-900 hover:text-primary-600 transition">
                {{ $post['title'] }}
            </a>
        </h2>

        <div class="flex-grow">
            @if(!empty($post['description']))
                <p class="text-gray-600 mb-4" itemprop="description">{{ Str::limit($post['description'], 120) }}</p>
            @else
                <p class="text-gray-600 mb-4" itemprop="description">{{ Str::limit(strip_tags(Str::markdown($post['content'])), 120) }}</p>
            @endif
        </div>

        <div>
            <a href="{{ route('blog.show', $post['slug']) }}"
               class="text-primary-600 hover:text-primary-800 font-medium inline-flex items-center"
               aria-label="Read full article: {{ $post['title'] }}">
                {{ __("Read more") }}
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
            </a>
        </div>
    </div>
</div>