@php
    $data = \Illuminate\Support\Facades\Cache::flexible(
        'skymage-30days-stats',
        [24 * 3600, 48 * 3600],
        fn() => app(App\Services\Bunny::class)->statistics()
    );
    // dd($data);
//    $data = [
//        'TotalRequestsServed' => 12300000,
//        'TotalBandwidthUsed' => 12300000000,
//        'CacheHitRate' => 69.6
//    ];
//
    $stats = [
        [
            'label' => __('Total requests'),
            'raw_value' => $data['TotalRequestsServed'],
            'icon' => 'bolt',
            'color' => 'primary',
        ],
        [
            'label' => __('Total bandwidth'),
            'raw_value' => $data['TotalBandwidthUsed'] / 1024 / 1024 /1024,
            'suffix' => ' GB',
            'icon' => 'server',
            'color' => 'blue',
        ],
        [
            'label' => __('Websites powered'),
            'raw_value' => 50,
            'suffix' => '+',
            'icon' => 'globe-alt',
            'color' => 'indigo',
        ]
    ]
@endphp
{{-- Powering the Web with Optimized Images --}}
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4">
        <h2 class="text-3xl font-semibold text-center text-gray-900 mb-12">
            {{ __('Powering the Web with Optimized Images') }}
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8"
         x-data="{
            animateNumbers: function() {
                const stats = document.querySelectorAll('[data-count]');
                stats.forEach(stat => {
                    const target = parseInt(stat.getAttribute('data-count'));
                    const suffix = stat.getAttribute('data-suffix') || '';
                    const duration = 2000; // ms
                    const start = 0;
                    const startTime = performance.now();

                    const updateCount = (currentTime) => {
                        const elapsedTime = currentTime - startTime;
                        if (elapsedTime < duration) {
                            const progress = elapsedTime / duration;
                            const easedProgress = progress === 1 ? 1 : 1 - Math.pow(2, -10 * progress);
                            const currentCount = Math.floor(easedProgress * (target - start) + start);
                            stat.textContent = new Intl.NumberFormat().format(currentCount) + suffix;
                            requestAnimationFrame(updateCount);
                        } else {
                            stat.textContent = new Intl.NumberFormat().format(target) + suffix;
                        }
                    };

                    requestAnimationFrame(updateCount);
                });
            }
         }"
         x-init="animateNumbers()"
    >
            @foreach ($stats as $stat)
                <div class="text-center p-6 bg-white rounded-lg border border-gray-100 shadow-md hover:shadow-lg transition-all duration-300">
                    <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary-100 text-primary-600 mb-4">
                        <x-dynamic-component :component="'heroicon-o-' . $stat['icon']" class="w-8 h-8" />
                    </div>
                    <div class="text-4xl font-bold text-gray-900 mb-2"
                         data-count="{{ $stat['raw_value'] }}"
                         data-suffix="{{ $stat['suffix'] ?? '' }}">
                        0
                    </div>
                    <div class="text-base text-gray-600">
                        {{ $stat['label'] }}
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
</section>
