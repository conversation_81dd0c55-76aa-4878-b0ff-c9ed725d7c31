<div
    x-data="{
        isOpen: false,
        imgSrc: '',
        imgUrl: '',
        imgWidth: 0,
        imgHeight: 0,

        openLightbox(src, url, size, name) {
            this.imgSrc = src;
            this.imgUrl = url || src;
            this.isOpen = true;
            document.body.classList.add('overflow-hidden');

            // Get image dimensions when loaded
            this.$nextTick(() => {
                const img = this.$refs.lightboxImage;
                if (img.complete) {
                    this.setImageDimensions(img);
                } else {
                    img.onload = () => this.setImageDimensions(img);
                }
            });
        },

        setImageDimensions(img) {
            this.imgWidth = img.naturalWidth;
            this.imgHeight = img.naturalHeight;
        },

        closeLightbox() {
            this.isOpen = false;
            document.body.classList.remove('overflow-hidden');
        }
    }"
    @keydown.escape.window="closeLightbox()"
    @open-lightbox.window="openLightbox($event.detail.src, $event.detail.url, $event.detail.size, $event.detail.name)"
    {{ $attributes }}
>
    {{ $slot }}

    <!-- Lightbox overlay -->
    <div
        x-show="isOpen"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/70"
        @click="closeLightbox()"
        style="display: none;"
    >
        <!-- Close button moved to top right of screen -->
        <button
            @click="closeLightbox()"
            class="fixed top-4 right-4 z-10 hover:bg-black/60 text-white rounded-full p-2 transition-colors"
            aria-label="Close lightbox"
        >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>

        <div @click.stop class="relative max-w-5xl max-h-[90vh] mx-4">
            <!-- Image container - removed padding -->
            <div class="flex items-center justify-center p-4">
                <img
                    x-ref="lightboxImage"
                    :src="imgSrc"
                    class="max-w-full max-h-[80vh] object-contain"
                    alt="Image preview"
                >
            </div>

            <!-- Image info with dimensions only -->
            <div class="p-4 text-white">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="text-sm bg-gray-700 px-2 py-1 rounded" x-show="imgWidth && imgHeight" x-text="`${imgWidth} × ${imgHeight}`"></div>
                    </div>
                </div>
                <div class="mt-2 font-mono text-xs text-gray-300 break-all" x-show="imgUrl">
                    <a :href="imgUrl" target="_blank" class="hover:text-white transition-colors hover:underline" x-text="imgUrl"></a>
                </div>
            </div>
        </div>
    </div>
</div>
