<div
    x-data="{
        tabSelected: 1,
        tabId: $id('tabs'),
        tabButtonClicked(tabButton){
            this.tabSelected = tabButton.id.replace(this.tabId + '-', '');
            this.tabRepositionMarker(tabButton);
        },
        tabRepositionMarker(tabButton){
            this.$refs.tabMarker.style.width=tabButton.offsetWidth + 'px';
            this.$refs.tabMarker.style.height=tabButton.offsetHeight + 'px';
            this.$refs.tabMarker.style.left=tabButton.offsetLeft + 'px';
        },
        tabContentActive(tabContent){
            return this.tabSelected == tabContent.id.replace(this.tabId + '-content-', '');
        },
        tabButtonActive(tabContent){
            const tabId = tabContent.id.split('-').slice(-1);
            return this.tabSelected == tabId;
        }
    }"
    x-init="tabRepositionMarker($refs.tabButtons.firstElementChild);" class="mt-12 relative w-full text-gray-500">

    <div x-ref="tabButtons" class="relative inline-grid items-center justify-center w-full h-10 grid-cols-3 p-1 bg-white border border-gray-100 rounded-lg select-none">
        {{ $triggers ?? '' }}
{{--        <div x-ref="tabMarker" class="absolute left-0 z-10 h-full duration-300 ease-out" x-cloak>--}}
{{--            <div class="w-full h-full bg-gray-100 rounded-md shadow-sm"></div>--}}
{{--        </div>--}}
    </div>
    <div class="relative pt-6 flex items-center justify-center w-full rounded-md content border-gray-200/70">
        {{ $slot }}
    </div>
</div>
