<x-section>
    <div class="text-center mb-16">
        <h2 class="text-3xl font-extrabold tracking-tight text-primary-600 sm:text-4xl">
            {{ __("Latest from our Blog") }}
        </h2>
        <p class="mt-4 max-w-2xl mx-auto text-base text-gray-500">
            {{ __("Learn more about image optimization and CDN technologies") }}
        </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        @inject('blogPostParser', 'App\Services\BlogPostParser')
        @php
            $posts = $blogPostParser->getLatestPosts(3);
        @endphp

        @foreach ($posts as $post)
            <article itemscope itemtype="https://schema.org/BlogPosting" class="h-full">
                <meta itemprop="datePublished" content="{{ $post['date']->toIso8601String() }}">
                <meta itemprop="author" content="Skymage Team">
                <meta itemprop="publisher" content="Skymage">
                <x-blog-post-card :post="$post" />
            </article>
        @endforeach
    </div>

    <div class="mt-12 text-center">
        <a href="{{ route('blog.index') }}"
           class="text-primary-600 hover:text-primary-800 font-medium inline-flex items-center text-lg"
           title="{{ __('Browse all articles about image optimization') }}"
           aria-label="{{ __('View all blog articles about image optimization and CDN') }}">
            {{ __("View all articles") }}
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
        </a>
    </div>
</x-section>