@props([
    'variant' => 'primary', // primary, outline, dark
    'size' => 'md', // sm, md, lg
    'icon' => 'fire'
])

@php
    $variantClasses = [
        'primary' => 'bg-primary-100 text-primary-800 border-primary-200',
        'outline' => 'bg-white text-gray-800 border-gray-300',
        'dark' => 'bg-gray-900 text-white border-gray-900'
    ];

    $sizeClasses = [
        'sm' => 'px-3 py-1 text-xs',
        'md' => 'px-4 py-2 text-sm',
        'lg' => 'px-6 py-3 text-base'
    ];

    $iconSizes = [
        'sm' => 'h-3 w-3',
        'md' => 'h-4 w-4',
        'lg' => 'h-5 w-5'
    ];
@endphp

<div {{ $attributes->merge(['class' => 'inline-flex items-center rounded-full font-semibold border ' . $variantClasses[$variant] . ' ' . $sizeClasses[$size]]) }}>
    <x-dynamic-component :component="'heroicon-s-' . $icon" :class="$iconSizes[$size] . ' mr-2'" />
    {{ $slot }}
</div>
