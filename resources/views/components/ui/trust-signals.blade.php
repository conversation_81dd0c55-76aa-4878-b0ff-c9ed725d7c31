@props([
    'signals' => [],
    'layout' => 'horizontal', // horizontal, vertical
    'size' => 'sm', // sm, md
    'variant' => 'default' // default, primary, minimal
])

@php
    $defaultSignals = [
        ['icon' => 'shield-check', 'text' => '30-day money-back guarantee'],
        ['icon' => 'credit-card', 'text' => 'No credit card required'],
        ['icon' => 'clock', 'text' => '5-minute setup']
    ];

    $signals = empty($signals) ? $defaultSignals : $signals;

    $layoutClasses = [
        'horizontal' => 'flex flex-wrap items-center gap-4 justify-center',
        'vertical' => 'space-y-2'
    ];

    $sizeClasses = [
        'sm' => 'text-sm',
        'md' => 'text-base'
    ];

    $iconSizes = [
        'sm' => 'h-4 w-4',
        'md' => 'h-5 w-5'
    ];

    $iconColor = $variant === 'primary' ? 'text-primary-500' : 'text-gray-500';
@endphp

<div {{ $attributes->merge(['class' => $layoutClasses[$layout] . ' ' . $sizeClasses[$size] . ' text-gray-600']) }}>
    @foreach($signals as $signal)
        <div class="flex items-center">
            <x-dynamic-component
                :component="'heroicon-s-' . $signal['icon']"
                :class="$iconSizes[$size] . ' ' . $iconColor . ' mr-2'"
            />
            {{ $signal['text'] }}
        </div>
    @endforeach
</div>
