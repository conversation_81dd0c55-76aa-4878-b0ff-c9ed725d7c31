@props([
    'icon',
    'title',
    'description',
    'variant' => 'primary' // primary, outline, minimal
])

@php
    $iconClasses = [
        'primary' => 'bg-primary-100 text-primary-600',
        'outline' => 'bg-white text-gray-600 border border-gray-300',
        'minimal' => 'bg-gray-100 text-gray-600'
    ];
@endphp

<div {{ $attributes->merge(['class' => 'flex items-start']) }}>
    <div class="flex-shrink-0">
        <div class="flex items-center justify-center h-8 w-8 rounded-full {{ $iconClasses[$variant] }}">
            <x-dynamic-component :component="'heroicon-s-' . $icon" class="h-5 w-5" />
        </div>
    </div>
    <div class="ml-3">
        <h4 class="text-lg font-semibold text-gray-900">{{ $title }}</h4>
        <p class="text-gray-600">{{ $description }}</p>
    </div>
</div>
