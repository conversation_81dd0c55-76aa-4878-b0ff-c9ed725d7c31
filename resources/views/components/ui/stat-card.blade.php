@props([
    'value',
    'label',
    'variant' => 'default', // default, bordered, minimal, negative
    'emphasis' => false // highlight important stats
])

@php
    $variantClasses = [
        'default' => 'bg-white p-6 rounded-xl shadow-md',
        'bordered' => 'bg-white p-6 rounded-xl shadow-md border-l-4 border-primary-500',
        'minimal' => 'p-4 rounded-lg bg-gray-50',
        'negative' => 'bg-white p-6 rounded-xl shadow-md border-l-4 border-gray-400'
    ];

    $valueClasses = [
        'default' => 'text-gray-900',
        'bordered' => 'text-primary-600',
        'minimal' => 'text-gray-900',
        'negative' => 'text-gray-600'
    ];
@endphp

<div {{ $attributes->merge(['class' => $variantClasses[$variant]]) }}>
    <div class="text-3xl font-bold {{ $valueClasses[$variant] }} mb-2">{{ $value }}</div>
    <p class="text-gray-700 font-medium">{{ $label }}</p>
</div>
