@props([
    'code' => 'SKYLAUNCH',
    'description' => '30% off lifetime • Valid until July 31st',
    'size' => 'md' // sm, md, lg
])

@php
    $sizeClasses = [
        'sm' => 'px-6 py-3 text-lg',
        'md' => 'px-8 py-4 text-xl',
        'lg' => 'px-10 py-6 text-2xl'
    ];

    $codeSizes = [
        'sm' => 'text-lg px-3 py-1',
        'md' => 'text-2xl px-4 py-2',
        'lg' => 'text-3xl px-6 py-3'
    ];
@endphp

<div {{ $attributes->merge(['class' => 'text-center']) }}>
    <div class="inline-block bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl shadow-lg {{ $sizeClasses[$size] }}">
        <p class="text-sm font-medium mb-2">{{ __('Use this code at checkout:') }}</p>
        <div class="flex items-center justify-center gap-3">
            <code class="font-bold tracking-wider bg-white/20 rounded-lg {{ $codeSizes[$size] }}">{{ $code }}</code>
            <button
                onclick="navigator.clipboard.writeText('{{ $code }}'); this.innerHTML='✓ Copied!'; setTimeout(() => this.innerHTML='📋 Copy', 2000)"
                class="bg-white/20 hover:bg-white/30 px-3 py-2 rounded-lg text-sm font-medium transition-all"
            >
                📋 Copy
            </button>
        </div>
        <p class="text-xs mt-2 opacity-90">{{ $description }}</p>
    </div>
</div>
