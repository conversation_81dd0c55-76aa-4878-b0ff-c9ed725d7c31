@props([
    'background' => 'white', // white, gray, gradient, primary, dark
    'padding' => 'default', // sm, default, lg
    'container' => true
])

@php
    $backgroundClasses = [
        'white' => 'bg-white',
        'gray' => 'bg-gray-50',
        'gradient' => 'bg-gradient-to-b from-gray-50 to-white',
        'primary' => 'bg-gradient-to-br from-primary-500 to-primary-600',
        'dark' => 'bg-gray-900'
    ];

    $paddingClasses = [
        'sm' => 'py-12',
        'default' => 'py-16 lg:py-20',
        'lg' => 'py-20 lg:py-24'
    ];
@endphp

<section {{ $attributes->merge(['class' => $backgroundClasses[$background] . ' ' . $paddingClasses[$padding] . ' relative']) }}>
    @if($container)
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {{ $slot }}
        </div>
    @else
        {{ $slot }}
    @endif
</section>
