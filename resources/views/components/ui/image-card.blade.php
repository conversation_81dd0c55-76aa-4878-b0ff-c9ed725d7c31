@props([
    'title' => '',
    'indicator' => 'gray',
    'metaInfo' => null,
    'image' => '',
    'alt' => 'Image',
    'clickable' => true,
    'showHeader' => true,
    'borderless' => false,
])

@php
    $indicatorClass = match ($indicator) {
        'gray' => 'bg-gray-400',
        'yellow' => 'bg-yellow-400',
        'green' => 'bg-green-400',
        'blue' => 'bg-blue-400',
        default => 'bg-gray-400',
    };
@endphp

<div {{ $attributes->merge(['class' => 'rounded-lg overflow-hidden ' . ($borderless ? '' : 'border bg-white')]) }}

    >
    @if($showHeader)
        <div class="p-3 bg-gray-50 flex justify-between items-center">
            <div @class([
                "flex items-center",
                "mx-auto" => $borderless,
                ])>
                <div class="w-3 h-3 {{ $indicatorClass }} rounded-full mr-2"></div>
                <span class="text-sm font-medium text-gray-600">{{ $title }}</span>
            </div>
            @if($metaInfo)
                {{ $metaInfo }}
            @endif
        </div>
    @endif
    <div class="h-[500px] flex items-center justify-center p-4">
        <img
            src="{{ $image }}"
            {{ $attributes->only(['x-bind:src'])->merge(['alt' => $alt]) }}
            @if($clickable)
                class="max-w-full max-h-full object-contain rounded-md cursor-pointer hover:opacity-90 transition-opacity"
                @click="$dispatch('open-lightbox', {src: $event.target.getAttribute('src'), url: $event.target.getAttribute('src')})"
            @else
                class="max-w-full max-h-full object-contain rounded-md"
            @endif
        >
    </div>
</div>
