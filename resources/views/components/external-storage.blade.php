{{-- Connect Your External Storage --}}
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
                {{ __('Connect Your') }} <span class="text-primary">{{ __('External Storage') }}</span>
            </h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
                {{ __('Optimize images from your own storage, stay in control, and deliver fast with our CDN.') }}
            </p>

            {{-- Supported Storage Providers --}}
            <div class="flex flex-wrap items-center justify-center gap-8 mb-12">
                <div class="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg shadow-sm">
                    <div class="w-8 h-8 bg-orange-100 rounded flex items-center justify-center">
                        <svg class="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700">{{ __('Amazon S3') }}</span>
                </div>
                <div class="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg shadow-sm">
                    <div class="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700">{{ __('DigitalOcean Spaces') }}</span>
                </div>
                <div class="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg shadow-sm">
                    <div class="w-8 h-8 bg-green-100 rounded flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700">{{ __('MinIO') }}</span>
                </div>
                <div class="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg shadow-sm">
                    <div class="w-8 h-8 bg-purple-100 rounded flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700">{{ __('S3 Compatible') }}</span>
                </div>
            </div>
        </div>

        {{-- How It Works --}}
        <div class="py-8 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="mb-6">
                        <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl font-bold text-white">1</span>
                        </div>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">{{ __('Connect Storage') }}</h4>
                    <p class="text-sm text-gray-600">{{ __('Link your S3, Spaces, or compatible storage with secure credentials') }}</p>
                </div>

                <div class="text-center">
                    <div class="mb-6">
                        <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl font-bold text-white">2</span>
                        </div>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">{{ __('Update URLs') }}</h4>
                    <p class="text-sm text-gray-600">{{ __('Update your website images urls with your Skymage CDN url. We') }}</p>
                </div>

                <div class="text-center">
                    <div class="mb-6">
                        <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl font-bold text-white">3</span>
                        </div>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">{{ __('Enjoy Speed') }}</h4>
                    <p class="text-sm text-gray-600">{{ __('Images are pull directly from your storage, cached globally, and delivered lightning-fast') }}</p>
                </div>
            </div>
        </div>

    </div>
</section>
