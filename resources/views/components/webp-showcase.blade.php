{{-- WEBP Image Compression --}}
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4">
        <div class="text-center mb-12">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mb-4">
                {{ __('Performance') }}
            </span>
            <h2 class="text-3xl font-semibold text-gray-900 mb-4">
                {{ __('WEBP Image Compression') }}
            </h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                {{ __('Our smart image optimization automatically converts and serves optimized WEBP images, reducing file size by up to 80%.') }}
            </p>
        </div>

        <div class="mt-10"
             x-data="{
                originalImage: 'https://daudau.cc/images/crab.png',
                originalSize: 480, // KB - adjusted for crab.png
                webpSize: 26, // KB - adjusted for crab.png conversion

                get compressionRatio() {
                    return Math.round((this.originalSize - this.webpSize) / this.originalSize * 100);
                },

                get originalUrl() {
                    return this.originalImage;
                },

                get webpUrl() {
                    return 'https://demo.skymage.net/v1/' + this.originalImage.replace(/^https?:\/\//, '') + '?format=webp';
                }
             }">
            {{-- Before/After Images Side by Side --}}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8 items-center">
                {{-- Before Image --}}
                <div class="flex flex-col items-center">
                    <div class="mb-4 w-full flex justify-center">
                        <img x-bind:src="originalUrl" alt="Original PNG image" class="h-[28rem] w-auto max-w-full object-contain rounded-xl shadow-xl border-4 border-red-200">
                    </div>
                    <div class="flex flex-col items-center">
                        <span class="text-lg font-semibold text-gray-700 mb-1">{{ __('Before (PNG)') }}</span>
                        <span class="text-lg font-bold text-red-600" x-text="`${originalSize} KB`"></span>
                    </div>
                </div>

                {{-- After Image --}}
                <div class="flex flex-col items-center">
                    <div class="mb-4 w-full flex justify-center">
                        <img x-bind:src="webpUrl" alt="Optimized WEBP image" class="h-[28rem] w-auto max-w-full object-contain rounded-xl shadow-xl border-4 border-green-200">
                    </div>
                    <div class="flex flex-col items-center">
                        <span class="text-lg font-semibold text-gray-700 mb-1">{{ __('After (WEBP)') }}</span>
                        <span class="text-lg font-bold text-green-600" x-text="`${webpSize} KB (-${compressionRatio}%)`"></span>
                    </div>
                </div>
            </div>


            {{-- Compression Stats --}}
            <div class="text-center">
                <div class="inline-flex items-center px-6 py-3 bg-green-100 text-green-800 rounded-full font-semibold">
                    <x-heroicon-s-arrow-down class="h-5 w-5 mr-2" />
                    <span x-text="`${compressionRatio}% smaller file size`"></span>
                </div>
            </div>
        </div>
    </div>
</div>
