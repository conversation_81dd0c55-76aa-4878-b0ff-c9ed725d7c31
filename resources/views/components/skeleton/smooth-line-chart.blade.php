@props([
    'title' => 'Chart',
    'legends' => ['Dataset 1'],
    'height' => 'h-64'
])

<div class="p-6">
    <div class="animate-pulse">
        <!-- Chart Header -->
        <div class="h-6 bg-gray-200 rounded w-32 mb-6"></div>

        <!-- Chart Area -->
        <div class="{{ $height }} bg-gray-100 rounded-lg relative overflow-hidden">
            <!-- Line Chart -->
            <div class="absolute inset-4">
                <svg class="w-full h-full" viewBox="0 0 300 200">
                    <path d="M10,180 Q50,120 90,140 T170,100 T250,130"
                          stroke="#e5e7eb"
                          stroke-width="3"
                          fill="none"
                          opacity="0.7"/>
                    @if(count($legends) > 1)
                        <path d="M10,180 Q50,160 90,150 T170,120 T250,140"
                              stroke="#e5e7eb"
                              stroke-width="2"
                              fill="none"
                              opacity="0.5"/>
                    @endif
                </svg>
            </div>
        </div>

        <!-- Legend -->
        <div class="flex space-x-6 mt-4">
            @foreach($legends as $legend)
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-gray-200 rounded"></div>
                    <div class="h-4 bg-gray-200 rounded w-{{ strlen($legend) + 8 }}"></div>
                </div>
            @endforeach
        </div>
    </div>
</div>
