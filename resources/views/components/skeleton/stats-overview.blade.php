@props([
    'cards' => 3
])

<div class="p-6">
    <div class="grid grid-cols-1 md:grid-cols-{{ $cards }} gap-6">
        @for($i = 0; $i < $cards; $i++)
            <div class="bg-white rounded-lg border border-gray-100 p-6">
                <div class="animate-pulse">
                    <div class="h-4 bg-gray-200 rounded w-{{ collect([24, 28, 32])->random() }} mb-2"></div>
                    <div class="h-8 bg-gray-200 rounded w-{{ collect([24, 32, 36])->random() }}"></div>
                </div>
            </div>
        @endfor
    </div>
</div>
