@props([
    'title' => 'Chart',
    'legends' => ['Dataset 1', 'Dataset 2'],
    'height' => 'h-64'
])

<div class="p-6">
    <div class="animate-pulse">
        <!-- Chart Header -->
        <div class="h-6 bg-gray-200 rounded w-28 mb-6"></div>

        <!-- Chart Area -->
        <div class="{{ $height }} bg-gray-100 rounded-lg relative overflow-hidden">
            <!-- Bar Chart Bars -->
            <div class="absolute bottom-0 left-0 w-full h-full flex items-end justify-around p-4">
                @for($i = 0; $i < 7; $i++)
                    <div class="bg-gray-200 rounded w-6" style="height: {{ rand(30, 85) }}%"></div>
                @endfor
            </div>
        </div>

        <!-- Legend -->
        <div class="flex space-x-6 mt-4">
            @foreach($legends as $legend)
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-gray-200 rounded"></div>
                    <div class="h-4 bg-gray-200 rounded w-{{ strlen($legend) + 8 }}"></div>
                </div>
            @endforeach
        </div>
    </div>
</div>
