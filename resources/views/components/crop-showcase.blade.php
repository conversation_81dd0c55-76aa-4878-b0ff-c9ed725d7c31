<div class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <x-ui.section-header
            tagline="Smart Cropping"
            title="Perfect Fit For Any Display"
            description="Automatically crop and resize images to fit different aspect ratios while preserving the important content."
        />

        <div class="mt-10"
             x-data="{
                cropOptions: {
                    'thumbnail': {
                        image: 'https://daudau.cc/images/perfume-sample.jpeg',
                        params: { w: 500, h: 500, fit: 'crop', p: 'attention' },
                        description: 'Automatically focuses on the most important part of the image - ideal for thumbnails and product photos'
                    },
                    'profile': {
                        image: 'https://images.unsplash.com/photo-1552058544-f2b08422138a',
                        params: { w: 300, h: 300, fit: 'cover'},
                        description: 'Square crop optimized for profile pictures and avatars - perfect for user photos'
                    },
                    'banner': {
                        image: 'https://daudau.cc/images/landscape-lake.png',
                        params: { w: 820, h: 322, fit: 'cover' },
                        description: 'Wide format cropping from the top of the image - ideal for headers and banners'
                    }
                },
                activeTab: 'thumbnail',

                isCropActive(tabName) {
                    return this.activeTab === tabName;
                },

                activateCrop(tabName) {
                    this.activeTab = tabName;
                },

                get currentOption() {
                    return this.cropOptions[this.activeTab];
                },

                get originalUrl() {
                    return this.currentOption.image;
                },

                get cropUrl() {
                    const { image, params } = this.currentOption;
                    const queryParams = Object.entries(params)
                        .map(([key, value]) => `${key}=${value}`)
                        .join('&');
                    return `https://demo.skymage.net/v1/${image.replace(/^https?:\/\//, '')}?${queryParams}`;
                },

                get cropDescription() {
                    return this.currentOption.description;
                },

                showOriginal: false,

                toggleOriginal() {
                    this.showOriginal = !this.showOriginal;
                }
             }">
            <!-- Action buttons using shared tab-button components -->
            <div class="mb-6 flex flex-col items-center">
                <div class="flex border-b">
                    <x-ui.tab-button
                        @click="activateCrop('thumbnail')"
                        :active="false"
                        x-bind:class="{'border-primary-600 text-primary-600': isCropActive('thumbnail'), 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': !isCropActive('thumbnail')}"
                        class="px-4 py-2 text-sm font-medium border-b-2 -mb-px bg-transparent shadow-none">
                        Smart Thumbnail
                    </x-ui.tab-button>

                    <x-ui.tab-button
                        @click="activateCrop('profile')"
                        :active="false"
                        x-bind:class="{'border-primary-600 text-primary-600': isCropActive('profile'), 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': !isCropActive('profile')}"
                        class="px-4 py-2 text-sm font-medium border-b-2 -mb-px bg-transparent shadow-none">
                        Profile Picture
                    </x-ui.tab-button>

                    <x-ui.tab-button
                        @click="activateCrop('banner')"
                        :active="false"
                        x-bind:class="{'border-primary-600 text-primary-600': isCropActive('banner'), 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': !isCropActive('banner')}"
                        class="px-4 py-2 text-sm font-medium border-b-2 -mb-px bg-transparent shadow-none">
                        Banner/Header
                    </x-ui.tab-button>
                </div>

                <div class="mt-4 text-center">
                    <p class="text-gray-600 text-sm" x-text="cropDescription"></p>
                </div>
            </div>

            <!-- Side by side comparison using image-card components -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Cropped image card -->
                <x-ui.image-card
                    title="Cropped Image"
                    indicator="blue"
                    clickable="true"
                    x-bind:src="cropUrl"
                    alt="Cropped image"
                    borderless
                >
                </x-ui.image-card>

                <!-- Original image card -->
                <x-ui.image-card
                    title="Original Image"
                    indicator="gray"
                    clickable="true"
                    x-bind:src="originalUrl"
                    alt="Original image"
                    borderless
                >
                </x-ui.image-card>
            </div>
        </div>
    </div>
</div>
