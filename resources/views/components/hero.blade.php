{{-- Hero Section --}}
<x-section
    class="lg:!pb-24 !pt-8 relative overflow-hidden"
    style="
        background-size: 40px 40px;
        background-image: linear-gradient(to right, #eee 1px, transparent 1px), linear-gradient(to bottom, #eee 1px, transparent 1px);
     ">
    <!-- Gradient overlay for blur effect -->
    <div class="absolute bottom-0 left-0 right-0 h-36 bg-gradient-to-t from-gray-50 to-transparent pointer-events-none"></div>

    <div class="max-w-7xl mx-auto px-4 items-center grid grid-cols-1 md:grid-cols-2 gap-8">
        <div class="text-center md:text-left">

            <!-- Main Headline - SEO optimized -->
            <h1 class="font-bold text-3xl lg:text-5xl lg:leading-[1.2]">
                {{ __('Image Optimization CDN for') }}
            <span id="headline-rolldown" class="text-primary transition-all duration-500">{{ __('Faster Websites') }}</span>
            </h1>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const words = [
                        'Faster Websites',
                        'Conversions',
                        'Boost SEO',
                    ];
                    let idx = 0;
                    const el = document.getElementById('headline-rolldown');
                    setInterval(() => {
                        idx = (idx + 1) % words.length;
                        el.style.opacity = 0;
                        setTimeout(() => {
                            el.textContent = words[idx];
                            el.style.opacity = 1;
                        }, 500);
                    }, 2000);
                });
            </script>

            <!-- Subheadline with specific benefits -->
            <p class="mt-6 text-gray-600 text-lg">
                {{ __('Instantly optimize every image for faster load times, better SEO, and higher conversions with easy integration. Simple setup, instant results.') }}
            </p>


            <!-- CTA Buttons with improved copy -->
            <div class="mt-8 flex flex-col sm:flex-row items-center sm:items-start gap-4 justify-start">
                <a href="/register" class="w-full sm:w-auto inline-block px-8 py-4 text-white bg-gradient-to-r from-primary to-primary-600 hover:from-primary-600 hover:to-primary-700 rounded-lg font-semibold transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-xl relative overflow-hidden group text-center text-lg">
                    <span class="absolute inset-0 bg-white/20 w-1/4 h-full transform -skew-x-12 -translate-x-full transition-transform group-hover:translate-x-[500%] ease-out duration-700"></span>
                    <span class="relative z-10 flex items-center justify-center">
                        {{ __('Get 30% Off Lifetime - Start Now') }}
                        <x-heroicon-o-arrow-right class="h-5 w-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" />
                    </span>
                </a>
            </div>


            <!-- Single trust signal -->
            <div class="mt-6">
                <x-ui.trust-signals :signals="[
                    ['icon' => 'shield-check', 'text' => '14-day free trial, no credit card required']
                ]" layout="horizontal" class="justify-center sm:justify-start" />
            </div>
        </div>

        <div>
            <x-playground/>
        </div>
    </div>
</x-section>
