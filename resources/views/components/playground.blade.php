<div class="bg-white p-6 rounded-xl bg-gray-100/10 border size-full"
     x-data="{
        cdnBaseUrl: 'https://demo.skymage.net/v1/',
        imageUrl: 'https://daudau.cc/images/crab.png',
        currentParam: '',
        displayedParam: '',
        typewriterIndex: 0,
        paramSets: [
            { name: 'Normal image', params: {} },
            { name: 'Width resize', params: { w: 322, h: 322 }, effect: 'resize' },
            { name: 'Cover crop resize', params: { w: 600, h: 150, fit: 'cover' }, effect: 'resize-crop' },
            { name: 'Vertical flip', params: { flip: 'v' }, effect: 'flip-v' },
            { name: 'Horizontal flip', params: { flip: 'h' }, effect: 'flip-h' },
            { name: 'Blur effect', params: { blur: 7 }, effect: 'blur' },
            { name: 'Sharpen effect', params: { sharp: 10 }, effect: 'blur' },
        ],
        preloadedImages: [],
        currentSetIndex: 0,
        nextParam: '',
        intervalDuration: 3000,
        animating: false,
        primaryImageOpacity: 100,

        init() {
            // Set initial parameter immediately
            this.setParam(this.paramSets[0]);
            // Start parameter loop immediately
            this.startParamLoop();
            // Preload images in background
            this.preloadImages();
        },

        selectAllText() {
            const range = document.createRange();
            range.selectNodeContents(this.$refs.urlDisplay);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
        },

        async preloadImages() {
            const preloadPromises = this.paramSets.map(paramSet => {
                return new Promise((resolve, reject) => {
                    const paramString = this.buildParamString(paramSet.params);
                    const imageUrl = this.cdnBaseUrl + this.processedImageUrl + paramString;

                    const img = new Image();
                    img.onload = () => {
                        this.preloadedImages.push({
                            url: imageUrl,
                            paramString: paramString
                        });
                        resolve();
                    };
                    img.onerror = reject;
                    img.src = imageUrl;
                });
            });

            try {
                await Promise.all(preloadPromises);
                console.log('All images preloaded successfully');
            } catch (error) {
                console.error('Error preloading images:', error);
            }
        },

        buildParamString(paramObj) {
            const entries = Object.entries(paramObj);
            if (entries.length === 0) return '';

            return '?' + entries.map(([key, value]) => `${key}=${value}`).join('&');
        },

        getCurrentParamName() {
            return this.paramSets[this.currentSetIndex].name;
        },

        setParam(paramSet) {
            this.nextParam = this.buildParamString(paramSet.params);
            this.typewriterEffect();
        },

        typewriterEffect() {
            // Reset typewriter state
            this.typewriterIndex = 0;
            this.displayedParam = '';
            // Begin typewriter animation
            this.typewriterTick();
        },

        typewriterTick() {
            if (this.typewriterIndex < this.nextParam.length) {
                this.displayedParam += this.nextParam.charAt(this.typewriterIndex);
                this.typewriterIndex++;
                setTimeout(() => this.typewriterTick(), 50);
            } else {
                // Typewriter effect finished, start image transition
                this.performTransition();
            }
        },

        performTransition() {
            // Start animation
            this.animating = true;

            // Fade out
            this.primaryImageOpacity = 0;

            // After fade out is complete
            setTimeout(() => {
                // Update the current parameter
                this.currentParam = this.nextParam;

                // Fade in
                this.primaryImageOpacity = 100;

                // End animation state
                setTimeout(() => {
                    this.animating = false;
                }, 300);
            }, 220);
        },

        get processedImageUrl() {
            return this.imageUrl.replace(/^https?:\/\//, '');
        },

        get cdnUrl() {
            return this.cdnBaseUrl + this.processedImageUrl + this.currentParam;
        },

        startParamLoop() {
            setInterval(() => {
                if (!this.animating) {
                    this.currentSetIndex = (this.currentSetIndex + 1) % this.paramSets.length;
                    this.setParam(this.paramSets[this.currentSetIndex]);
                }
            }, this.intervalDuration);
        }
     }"
>
    <div class="flex justify-center items-center h-[350px] md:h-[500px] relative overflow-hidden">
        <div class="flex justify-center items-center max-w-full max-h-full rounded-lg overflow-hidden">
            <img
                :src="cdnUrl"
                :style="`opacity: ${primaryImageOpacity}%;`"
                class="max-w-full rounded-md mx-auto transition-opacity duration-200 ease-in-out object-contain cursor-pointer hover:opacity-90"
                @click="$dispatch('open-lightbox', {src: cdnUrl, url: cdnUrl})"
            >
        </div>
    </div>

    <div>
        <div class="w-full mt-4 p-2 border bg-gray-100 rounded-md font-mono text-xs break-all"
             x-ref="urlDisplay" @dblclick="selectAllText()">
            <span class="bg-gray-200 text-gray-700 p-0.5 rounded-md inline-block" x-text="cdnBaseUrl.replace(/\/$/, '')"></span><span class="text-gray-700 inline-block">/</span><span class="text-gray-800 inline-block" x-text="processedImageUrl"></span><span class="text-primary-500 inline-block" x-text="displayedParam"></span>
        </div>
        <div class="mt-2 text-sm text-gray-500">
            Current transformation: <span x-text="getCurrentParamName()" class="font-medium"></span>
        </div>
    </div>

    <!-- Hidden div for preloaded images -->
    <div class="hidden">
        <template x-for="image in preloadedImages" :key="image.url">
            <img :src="image.url" alt="Preloaded image">
        </template>
    </div>
</div>
