<div class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <x-ui.section-header
            tagline="Branding"
            title="Watermark Your Images"
            description="Protect your visual content and strengthen your brand identity with customizable text watermarks."
        />

        <div class="mt-10 max-w-5xl mx-auto"
             x-data="{
                originalImage: 'https://daudau.cc/images/crab.png',
                watermarkText: 'Skymage',
                commonWatermarks: [
                    'Skymage',
                    '© 2025 Skymage',
                    'skymage.net',
                    'Photo by <PERSON>',
                    'CONFIDENTIAL',
                    'DRAFT'
                ],

                setWatermark(text) {
                    this.watermarkText = text;
                },

                get watermarkUrl() {
                    const params = { watermark: this.watermarkText, w: 600 };
                    const queryParams = Object.entries(params)
                        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
                        .join('&');
                    return `https://demo.skymage.net/v1/${this.originalImage.replace(/^https?:\/\//, '')}?${queryParams}`;
                }
             }">

            <div class="flex flex-col lg:flex-row gap-6">
                <!-- Left Side: Image Preview -->
                <div class="lg:w-2/3">
                    <div class="flex justify-center bg-gray-50">
                        <img
                            :src="watermarkUrl"
                            alt="Watermark preview"
                            class="max-w-full max-h-96 object-contain rounded cursor-pointer hover:opacity-90 transition-opacity"
                            @click="$dispatch('open-lightbox', {src: $event.target.getAttribute('src'), url: $event.target.getAttribute('src')})"
                        >
                    </div>
                </div>

                <!-- Right Side: Controls -->
                <div class="lg:w-1/3 space-y-4">
                    <!-- Custom Input -->
                    <div class="bg-white rounded-lg shadow-sm p-4">
                        <label for="watermark-text" class="block text-sm font-medium text-gray-700 mb-3">Custom
                            Watermark</label>
                        <input
                            type="text"
                            id="watermark-text"
                            x-model="watermarkText"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 text-sm"
                            placeholder="Enter watermark text"
                        >
                        <p class="text-xs text-gray-500 mt-2">Appears in bottom-right corner</p>
                    </div>

                    <!-- Common Watermarks -->
                    <div class="bg-white rounded-lg shadow-sm p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Common Examples</h4>
                        <div class="flex flex-wrap gap-2">
                            <template x-for="watermark in commonWatermarks" :key="watermark">
                                <button
                                    @click="setWatermark(watermark)"
                                    class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-gray-100 text-gray-700 hover:bg-primary-100 hover:text-primary-700 transition-colors cursor-pointer"
                                    :class="watermarkText === watermark ? 'bg-primary-100 text-primary-700' : ''"
                                    x-text="watermark"
                                ></button>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
