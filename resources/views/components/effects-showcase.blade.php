<div class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <x-ui.section-header
            tagline="Image Effects"
            title="Enhance Your Visuals"
            description="Apply professional-grade image effects and filters with simple URL parameters."
        />

        <div class="mt-10"
             x-data="{
                originalImage: 'https://daudau.cc/images/landscape-lake.png',
                effectOptions: {
                    'original': {
                        name: 'Original',
                        params: { w: 300 }
                    },
                    'blur': {
                        name: 'Blur',
                        params: { blur: 3, w: 300 }
                    },
                    'sharpen': {
                        name: 'Sharpen',
                        params: { sharp: 4, w: 300 }
                    },
                    'grayscale': {
                        name: '<PERSON><PERSON><PERSON>',
                        params: { filt: 'grayscale', w: 300 }
                    },
                    'sepia': {
                        name: 'Sepia',
                        params: { filt: 'sepia', w: 300 }
                    },
                    'negative': {
                        name: 'Negative',
                        params: { filt: 'negative', w: 300 }
                    },
                    'brightness': {
                        name: 'Brightness',
                        params: { bri: 69, w: 300 }
                    },
                    'contrast': {
                        name: 'Contrast',
                        params: { con: 69, w: 300 }
                    }
                },

                getEffectUrl(effect) {
                    const params = this.effectOptions[effect].params;
                    const queryParams = Object.entries(params)
                        .map(([key, value]) => `${key}=${value}`)
                        .join('&');
                    return `https://demo.skymage.net/v1/${this.originalImage.replace(/^https?:\/\//, '')}?${queryParams}`;
                }
             }">

            <!-- Effects Grid -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <template x-for="(effect, key) in effectOptions" :key="key">
                    <div class="group">
                        <!-- Thumbnail -->
                        <div class="aspect-square flex items-center justify-center mb-2">
                            <img
                                :src="getEffectUrl(key)"
                                :alt="effect.name + ' effect'"
                                class="w-full h-full object-cover rounded cursor-pointer hover:opacity-90 transition-opacity"
                                loading="lazy"
                                @click="$dispatch('open-lightbox', {src: $event.target.getAttribute('src'), url: $event.target.getAttribute('src')})"
                            >
                        </div>
                        <!-- Effect Name -->
                        <div class="text-center">
                            <span
                                class="text-sm font-medium text-gray-700"
                                x-text="effect.name"
                            ></span>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</div>
