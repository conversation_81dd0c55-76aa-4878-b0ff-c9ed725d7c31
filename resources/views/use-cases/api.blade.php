<x-layouts.marketing>
    <x-slot name="title">{{ __('API Integration | Use Cases | Skymage') }}</x-slot>
    <x-slot name="description">{{ __('Explore how developers use Skymage\'s powerful API to integrate advanced image optimization into their applications.') }}</x-slot>

    <!-- Hero Section -->
    <x-use-cases.hero
        :title="__('Powerful Image Processing API')"
        :description="__('Integrate advanced image optimization into your applications with our simple, flexible, and robust API.')"
        :image="'https://demo.skymage.net/v1/images.unsplash.com/photo-1555066931-4365d14bab8c'"
    />

    <!-- Benefits Section -->
    <div class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">{{ __('Benefits for Developers') }}</h2>

            <div class="grid md:grid-cols-3 gap-8">
                <x-use-cases.benefit-card
                    :title="__('Simple Integration')"
                    :description="__('Implement advanced image processing with just a few lines of code in any programming language.')"
                    icon="code-bracket"
                />

                <x-use-cases.benefit-card
                    :title="__('Offload Processing')"
                    :description="__('Free up your servers by letting us handle resource-intensive image transformations in the cloud.')"
                    icon="cpu-chip"
                />

                <x-use-cases.benefit-card
                    :title="__('Flexible Transformations')"
                    :description="__('Resize, crop, filter, and transform images with a simple URL-based API that\'s easy to customize.')"
                    icon="puzzle-piece"
                />
            </div>
        </div>
    </div>

    <!-- Code Example Section -->
    <div class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">{{ __('Simple Integration') }}</h2>

            <div class="grid md:grid-cols-2 gap-8 items-center">
                <div>
                    <h3 class="text-2xl font-semibold mb-4">{{ __('Just a Few Lines of Code') }}</h3>
                    <p class="text-gray-700 mb-6">{{ __('Our API is designed to be developer-friendly, with intuitive URL patterns and comprehensive documentation. Transform images on the fly with simple parameters.') }}</p>
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <x-heroicon-o-check-circle class="w-6 h-6 text-primary mr-2 flex-shrink-0 mt-0.5" />
                            <span>{{ __('URL-based transformations') }}</span>
                        </li>
                        <li class="flex items-start">
                            <x-heroicon-o-check-circle class="w-6 h-6 text-primary mr-2 flex-shrink-0 mt-0.5" />
                            <span>{{ __('Client libraries for popular languages') }}</span>
                        </li>
                        <li class="flex items-start">
                            <x-heroicon-o-check-circle class="w-6 h-6 text-primary mr-2 flex-shrink-0 mt-0.5" />
                            <span>{{ __('Webhooks for event-driven workflows') }}</span>
                        </li>
                    </ul>
                </div>
                <div class="bg-gray-900 p-6 rounded-lg">
                    <pre class="text-gray-100 overflow-x-auto"><code class="language-javascript">// Resize an image to 300x200
const imageUrl = "https://cdn.skymage.com/your-image.jpg?w=300&h=200";

// Apply a filter and crop
const filteredUrl = "https://cdn.skymage.com/your-image.jpg?w=500&crop=faces&filter=sepia";

// Smart compression with WebP when supported
const optimizedUrl = "https://cdn.skymage.com/your-image.jpg?auto=compress,format";</code></pre>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">{{ __('Key API Features') }}</h2>

            <div class="grid md:grid-cols-2 gap-x-12 gap-y-16">
                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                            <x-heroicon-o-bolt class="w-5 h-5 text-primary" />
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2">{{ __('Real-time Transformations') }}</h3>
                        <p class="text-gray-700">{{ __('Process images on-the-fly with multiple transformation parameters in a single API call.') }}</p>
                    </div>
                </div>

                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                            <x-heroicon-o-cog-6-tooth class="w-5 h-5 text-primary" />
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2">{{ __('Advanced Controls') }}</h3>
                        <p class="text-gray-700">{{ __('Fine-tune quality, format, and compression levels to balance performance and visual fidelity.') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.marketing>
