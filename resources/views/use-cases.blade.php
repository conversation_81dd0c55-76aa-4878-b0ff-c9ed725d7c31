<x-layouts.marketing>
    <x-slot name="title">{{ __('Use Cases | Skymage') }}</x-slot>

    <div class="container mx-auto px-4 py-12">
        <h1 class="text-4xl font-bold text-center mb-8">{{ __('Use Cases') }}</h1>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            @php
                $useCases = [
                    [
                        'title' => __('E-commerce Integration'),
                        'description' => __('Learn how Skymage can power your e-commerce platform with seamless payment processing and customer management.'),
                        'icon' => 'shopping-cart',
                        'route' => 'use-cases.ecommerce'
                    ],
                    [
                        'title' => __('Content Management'),
                        'description' => __('See how content creators use Skymage to manage and distribute their digital products efficiently.'),
                        'icon' => 'document-text',
                        'route' => 'use-cases.content-management'
                    ],
                    [
                        'title' => __('Subscription Services'),
                        'description' => __('Discover how Skymage simplifies subscription management for businesses of all sizes.'),
                        'icon' => 'credit-card',
                        'route' => 'use-cases.subscription'
                    ],
                    [
                        'title' => __('API Integration'),
                        'description' => __('Explore the possibilities of integrating Skymage\'s powerful API with your existing systems.'),
                        'icon' => 'code-bracket',
                        'route' => 'use-cases.api'
                    ],
                    [
                        'title' => __('Analytics & Reporting'),
                        'description' => __('Learn how businesses leverage Skymage\'s analytics to make data-driven decisions.'),
                        'icon' => 'chart-bar',
                        'route' => 'use-cases.analytics'
                    ]
                ];
            @endphp

            @foreach($useCases as $useCase)
                <div class="bg-white shadow-lg rounded-lg p-6">
                    <div class="flex justify-center mb-4">
                        <x-dynamic-component :component="'heroicon-o-' . $useCase['icon']" class="w-12 h-12 text-primary" />
                    </div>
                    <a href="{{ route($useCase['route']) }}" class="block hover:text-primary-700">
                        <h2 class="text-xl font-semibold mb-4 text-center">{{ $useCase['title'] }}</h2>
                    </a>
                    <p class="text-gray-700 mb-4">{{ $useCase['description'] }}</p>
                    <a href="{{ route($useCase['route']) }}" class="text-primary hover:text-primary-700 inline-flex items-center">
                        {{ __('Read more') }}
                        <x-heroicon-o-arrow-right class="ml-1 w-4 h-4" />
                    </a>
                </div>
            @endforeach
        </div>

        <div class="mt-12 text-center">
            <h3 class="text-2xl font-semibold mb-4">{{ __('Need a custom solution?') }}</h3>
            <p class="text-gray-700 mb-6 max-w-2xl mx-auto">{{ __('Our team can help you implement Skymage for your specific business needs.') }}</p>
            <a href="/register" class="bg-primary hover:bg-primary-600 text-white px-6 py-3 rounded-lg inline-flex items-center">
                <x-heroicon-o-chat-bubble-left-right class="w-5 h-5 mr-2" />
                {{ __('Start Free Trial') }}
            </a>
        </div>
    </div>
</x-layouts.marketing>
