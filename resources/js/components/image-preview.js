document.addEventListener('alpine:init', () => {
    Alpine.data('imagePreview', () => ({
        width: 520,
        quality: 80,
        activeTab: 'resize',
        baseImage: '/demo/bunny.jpg',

        previewUrl() {
            return `https://handle.skymage.net/v1${this.baseImage}?w=${this.width}&q=${this.quality}`;
        },

        generateUrl() {
            return `${this.baseImage}?w=${this.width}&q=${this.quality}`;
        },

        updatePreview() {
            // Any additional preview update logic
        }
    }));
});