<?php

use App\Actions\SubscribeToPlan;
use App\Models\PullZone;
use App\Models\User;
use App\Services\Bunny;
use LemonSqueezy\Laravel\Subscription;

it('updates pull zone bandwidth limit when user subscribes', function () {
    // Arrange
    $user = User::factory()->create();
    PullZone::factory()->create([
        'user_id' => $user->id,
        'id_bunny' => 123,
    ]);
    $user->createAsCustomer(['trial_ends_at' => now()->addDays(14)]);
    $plan = config('billing.plans')[1];
    $user->subscriptions()->create(array_merge(Subscription::factory()->make()->toArray(), [
        'variant_id' => $plan['monthly_id'],
        'type' => Subscription::DEFAULT_TYPE,
    ]));


    // Mock the Bunny service
    $this->mock(Bunny::class, function (Mockery\MockInterface $mock) use ($user) {
        $mock->shouldReceive('updatePullZone')
            ->once()
            ->with($user->pullZone->id_bunny, [
                'MonthlyBandwidthLimit' => $user->bandwidthLimitInGb() * 1000 * 1000 * 1000,
            ]);

        $mock->shouldReceive('getPullZone')
            ->once()
            ->with($user->pullZone->id_bunny);
    });

    // Act
    SubscribeToPlan::make()->handle($user);
});
