<?php

use App\Enums\ExternalStorageDriver;
use App\Enums\ExternalStorageStatus;
use App\Models\ExternalStorage;
use App\Models\User;

test('user can create external storage', function () {
    $user = User::factory()->create();

    $storage = ExternalStorage::create([
        'user_id' => $user->id,
        'name' => 'Test S3 Storage',
        'handle' => 'test-s3-storage',
        'driver' => ExternalStorageDriver::S3,
        'configuration' => [
            'access_key_id' => 'test-access-key',
            'secret_access_key' => 'test-secret-key',
            'bucket' => 'test-bucket',
            'region' => 'us-east-1',
        ],
    ]);

    expect($storage)
        ->driver->toBe(ExternalStorageDriver::S3)
        ->status->toBe(ExternalStorageStatus::Disconnected)
        ->handle->toBe('test-s3-storage');

    $this->assertDatabaseHas('external_storages', [
        'user_id' => $user->id,
        'name' => 'Test S3 Storage',
        'handle' => 'test-s3-storage',
        'driver' => 's3',
        'status' => 'disconnected',
    ]);
});

test('handle is unique within user scope', function () {
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();

    // Both users can have the same handle
    $storage1 = ExternalStorage::create([
        'user_id' => $user1->id,
        'name' => 'Storage 1',
        'handle' => 'same-handle',
        'driver' => 's3',
        'configuration' => ['test' => 'data'],
    ]);

    $storage2 = ExternalStorage::create([
        'user_id' => $user2->id,
        'name' => 'Storage 2',
        'handle' => 'same-handle',
        'driver' => 's3',
        'configuration' => ['test' => 'data'],
    ]);

    expect($storage1->handle)->toBe('same-handle');
    expect($storage2->handle)->toBe('same-handle');

    // But one user cannot have duplicate handles
    expect(function () use ($user1) {
        ExternalStorage::create([
            'user_id' => $user1->id,
            'name' => 'Duplicate Storage',
            'handle' => 'same-handle',
            'driver' => 's3',
            'configuration' => ['test' => 'data'],
        ]);
    })->toThrow(\Illuminate\Database\QueryException::class);
});

test('handle is required', function () {
    $user = User::factory()->create();

    expect(function () use ($user) {
        ExternalStorage::create([
            'user_id' => $user->id,
            'name' => 'Storage without handle',
            'driver' => 's3',
            'configuration' => ['test' => 'data'],
        ]);
    })->toThrow(\Illuminate\Database\QueryException::class);
});

test('user can only see their own storage', function () {
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();

    $storage1 = ExternalStorage::factory()->for($user1)->create(['name' => 'User 1 Storage']);
    $storage2 = ExternalStorage::factory()->for($user2)->create(['name' => 'User 2 Storage']);

    expect($user1->externalStorages()->count())->toBe(1);
    expect($user2->externalStorages()->count())->toBe(1);

    expect($user1->externalStorages->contains($storage1))->toBeTrue();
    expect($user1->externalStorages->contains($storage2))->toBeFalse();
});

test('external storage relationships work correctly', function () {
    $user = User::factory()->create();
    $storage = ExternalStorage::factory()->for($user)->create();

    expect($storage->user->id)->toBe($user->id);
    expect($user->externalStorages->contains($storage))->toBeTrue();
});

test('bunny storage connection test validates configuration', function () {
    $user = User::factory()->create();

    // Test with missing API key
    $storage = ExternalStorage::create([
        'user_id' => $user->id,
        'name' => 'Invalid Bunny Storage',
        'handle' => 'invalid-bunny',
        'driver' => ExternalStorageDriver::Bunny,
        'configuration' => [
            'storage_zone' => 'test-zone',
            'region' => 'de',
        ],
    ]);

    $result = $storage->testConnection();
    expect($result['success'])->toBeFalse();
    expect($result['message'])->toContain('API Key is required');

    // Test with missing storage zone
    $storage2 = ExternalStorage::create([
        'user_id' => $user->id,
        'name' => 'Invalid Bunny Storage 2',
        'handle' => 'invalid-bunny-2',
        'driver' => ExternalStorageDriver::Bunny,
        'configuration' => [
            'api_key' => 'test-api-key',
            'region' => 'de',
        ],
    ]);

    $result2 = $storage2->testConnection();
    expect($result2['success'])->toBeFalse();
    expect($result2['message'])->toContain('Storage Zone is required');
});

test('bunny storage provides detailed error messages', function () {
    $user = User::factory()->create();

    // Test detailed error message for missing API key
    $storage = ExternalStorage::create([
        'user_id' => $user->id,
        'name' => 'Missing API Key Storage',
        'handle' => 'missing-api-key',
        'driver' => ExternalStorageDriver::Bunny,
        'configuration' => [
            'storage_zone' => 'test-zone',
            'region' => 'de',
        ],
    ]);

    $result = $storage->testConnection();
    expect($result)->toHaveKeys(['success', 'message']);
    expect($result['success'])->toBeFalse();
    expect($result['message'])->toBe('API Key is required but not provided');

    // Test detailed error message for missing storage zone
    $storage2 = ExternalStorage::create([
        'user_id' => $user->id,
        'name' => 'Missing Storage Zone',
        'handle' => 'missing-storage-zone',
        'driver' => ExternalStorageDriver::Bunny,
        'configuration' => [
            'api_key' => 'test-api-key',
            'region' => 'de',
        ],
    ]);

    $result2 = $storage2->testConnection();
    expect($result2)->toHaveKeys(['success', 'message']);
    expect($result2['success'])->toBeFalse();
    expect($result2['message'])->toBe('Storage Zone is required but not provided');
});

test('s3 storage connection test validates configuration', function () {
    $user = User::factory()->create();

    // Test with missing access key ID
    $storage = ExternalStorage::create([
        'user_id' => $user->id,
        'name' => 'Invalid S3 Storage',
        'handle' => 'invalid-s3',
        'driver' => ExternalStorageDriver::S3,
        'configuration' => [
            'secret_access_key' => 'test-secret',
            'bucket' => 'test-bucket',
            'region' => 'us-east-1',
        ],
    ]);

    $result = $storage->testConnection();
    expect($result['success'])->toBeFalse();
    expect($result['message'])->toContain('Access Key ID is required');

    // Test with missing secret access key
    $storage2 = ExternalStorage::create([
        'user_id' => $user->id,
        'name' => 'Invalid S3 Storage 2',
        'handle' => 'invalid-s3-2',
        'driver' => ExternalStorageDriver::S3,
        'configuration' => [
            'access_key_id' => 'test-access-key',
            'bucket' => 'test-bucket',
            'region' => 'us-east-1',
        ],
    ]);

    $result2 = $storage2->testConnection();
    expect($result2['success'])->toBeFalse();
    expect($result2['message'])->toContain('Secret Access Key is required');

    // Test with missing bucket
    $storage3 = ExternalStorage::create([
        'user_id' => $user->id,
        'name' => 'Invalid S3 Storage 3',
        'handle' => 'invalid-s3-3',
        'driver' => ExternalStorageDriver::S3,
        'configuration' => [
            'access_key_id' => 'test-access-key',
            'secret_access_key' => 'test-secret',
            'region' => 'us-east-1',
        ],
    ]);

    $result3 = $storage3->testConnection();
    expect($result3['success'])->toBeFalse();
    expect($result3['message'])->toContain('Bucket name is required');
});

test('s3 storage provides detailed error messages', function () {
    $user = User::factory()->create();

    // Test detailed error message for missing access key ID
    $storage = ExternalStorage::create([
        'user_id' => $user->id,
        'name' => 'Missing Access Key Storage',
        'handle' => 'missing-access-key',
        'driver' => ExternalStorageDriver::S3,
        'configuration' => [
            'secret_access_key' => 'test-secret',
            'bucket' => 'test-bucket',
            'region' => 'us-east-1',
        ],
    ]);

    $result = $storage->testConnection();
    expect($result)->toHaveKeys(['success', 'message']);
    expect($result['success'])->toBeFalse();
    expect($result['message'])->toBe('Access Key ID is required but not provided');

    // Test detailed error message for missing secret access key
    $storage2 = ExternalStorage::create([
        'user_id' => $user->id,
        'name' => 'Missing Secret Key Storage',
        'handle' => 'missing-secret-key',
        'driver' => ExternalStorageDriver::S3,
        'configuration' => [
            'access_key_id' => 'test-access-key',
            'bucket' => 'test-bucket',
            'region' => 'us-east-1',
        ],
    ]);

    $result2 = $storage2->testConnection();
    expect($result2)->toHaveKeys(['success', 'message']);
    expect($result2['success'])->toBeFalse();
    expect($result2['message'])->toBe('Secret Access Key is required but not provided');

    // Test detailed error message for missing bucket
    $storage3 = ExternalStorage::create([
        'user_id' => $user->id,
        'name' => 'Missing Bucket Storage',
        'handle' => 'missing-bucket',
        'driver' => ExternalStorageDriver::S3,
        'configuration' => [
            'access_key_id' => 'test-access-key',
            'secret_access_key' => 'test-secret',
            'region' => 'us-east-1',
        ],
    ]);

    $result3 = $storage3->testConnection();
    expect($result3)->toHaveKeys(['success', 'message']);
    expect($result3['success'])->toBeFalse();
    expect($result3['message'])->toBe('Bucket name is required but not provided');
});
