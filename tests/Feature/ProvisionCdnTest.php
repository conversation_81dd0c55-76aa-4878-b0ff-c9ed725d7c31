<?php

use App\Actions\CreateCloudflareDomain;
use App\Actions\ProvisionCDN;
use App\Actions\PullZone\AddHostNameToPullZone;
use App\Actions\PullZone\CreateBunnyPullZone;
use App\Actions\PullZone\LoadFreeCertificate;

test('we can provision the CDN for the user', function () {
    $user = \App\Models\User::factory()->create();

    CreateBunnyPullZone::shouldRun()->once();
    AddHostNameToPullZone::shouldRun()->once();
    CreateCloudflareDomain::shouldRun()->once();
    LoadFreeCertificate::shouldRun()->once();

    app(ProvisionCDN::class)->handle($user);
});
